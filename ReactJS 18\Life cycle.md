# Life cycle

## `Class Component`

Gồm 3 giai đoạn chính:

- <PERSON><PERSON><PERSON> ra (Mounting)
- Thay đổi (Updating)
- Hủy bỏ (UnMounting)

### 1. ComponentDidMount()

- Chạy `1 lần sau khi` component render lần đâu tiên

### 2. ComponentDidUpdate()

- Chạy `mỗi khi` component render lại
- Khi có props thay đổi, state thay đổi hoặc bắt buộc update (forceUpdate)

### 3. ComponentWillUnMount()

- Chạy `trước khi` component hủy bỏ

-------------------------------------------------

## `Function Component`

### 1. useEffect luôn gọi mỗi khi component render (ComponentDidUpdate)

- Khi không truyền [] Dependencies

```js
useEffect(() => {
  console.log("I run");
});
```

### 2. useEffect gọi 1 lần sau khi component render lần đầu tiên (ComponentDidMount)

- Khi truyền Dependencies là []

```js
useEffect(() => {
  console.log("I run");
}, []);
```

### 3. useEffect gọi mỗi khi giá trị Dependencies thay đổi

- Khi truyền Dependencies là [test]

```js
useEffect(() => {
  console.log("I run");
}, [test]);
```

### 4. useEffect gọi trước khi component bị detroy (ComponentWillUnMount)

- Khicó hàm return trong useEffect

```js
useEffect(() => {
  console.log("I run");
  return () => {
    // Xử lý trước khi component detroy
  };
}, [test]);
```
