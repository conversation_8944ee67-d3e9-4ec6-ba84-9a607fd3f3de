# React Router DOM

## BrowserRouter

- <PERSON><PERSON> bọc ứng dụng để cung cấp khả năng điều hướng
- Component cung cấp khả năng điều hướng sử dụng `lịch sử trình duyệt`

```tsx
const Root = () => (
  <BrowserRouter>
    <App />
  </BrowserRouter>
);
```

## Routes và Route

- `Routes` là một component chứa nhiều `Route`.
- Mỗi `Route` định nghĩa một `tuyến đường` trong ứng dụng

```tsx
<Routes>
  <Route path="/" element={<Home />} />
  <Route path="/about" element={<About />} />
</Routes>
```

## Link

- Thay vì sử dụng thẻ `<a>`, bạn sử dụng `<Link>` để điều hướng mà `không làm mới trang`.

## NavLink

- <PERSON><PERSON> thể thêm các `class active`. Hữu ích cho việc tạo menu điều hướng với trạng thái "đang hoạt động".

## useParams

- Hook cho phép bạn truy cập các `tham số` của `URL`.

```tsx
// URL: /user/:id
const { id } = useParams();
```

## useNavigate

- Hook cho phép bạn `điều hướng` chương trình từ một `component`

```tsx
const navigate = useNavigate();

const goToHome = () => {
  navigate("/");
};
```

## useLocation

- Hook cho phép bạn truy cập thông tin về vị trí hiện tại của ứng dụng, bao gồm cả `pathname`, `search`, và `hash`.

```tsx
const LocationDisplay = () => {
  const { pathname, search, hash } = useLocation();

  return <div></div>;
};
```

## DEMO

```tsx
const Home = () => <h2>Home</h2>;

const User = () => {
  const { id } = useParams();
  return <h2>User ID: {id}</h2>;
};

const App = () => {
  const navigate = useNavigate();
  const { pathname, search, hash } = useLocation();

  const goToHome = () => {
    navigate("/");
  };

  return (
    <div>
      <nav>
        <ul>
          <li>
            <Link to="/">Home</Link>
          </li>
          <li></li>
          <li>
            <Link to="/user/123">User 123</Link>
          </li>
        </ul>
      </nav>

      <button onClick={goToHome}>Go to Home</button>

      <Routes>
        <Route path="/" element={<Home />} />
        <Route path="/user/:id" element={<User />} />
      </Routes>
    </div>
  );
};

// Bao bọc App với BrowserRouter
const Root = () => (
  <BrowserRouter>
    <App />
  </BrowserRouter>
);

export default Root;
```
