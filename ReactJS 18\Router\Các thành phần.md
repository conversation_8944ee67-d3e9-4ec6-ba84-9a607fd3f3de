# React Router DOM

## BrowserRouter

- <PERSON><PERSON> bọc ứng dụng để cung cấp khả năng điều hướng
- Component cung cấp khả năng điều hướng sử dụng `lịch sử trình duyệt`

```tsx
const Root = () => (
  <BrowserRouter>
    <App />
  </BrowserRouter>
);
```

## Routes và Route

- `Routes` là một component chứa nhiều `Route`.
- Mỗi `Route` định nghĩa một `tuyến đường` trong ứng dụng

```tsx
<Routes>
  <Route path="/" element={<Home />} />
  <Route path="/about" element={<About />} />
</Routes>
```

## Link

- Thay vì sử dụng thẻ `<a>`, bạn sử dụng `<Link>` để điều hướng mà `không làm mới trang`.

## NavLink

- <PERSON><PERSON> thể thêm các `class active`. Hữu ích cho việc tạo menu điều hướng với trạng thái "đang hoạt động".

## useParams

- Hook cho phép bạn truy cập các `tham số` của `URL`.

```tsx
// URL: /user/:id
const { id } = useParams();
```

## useNavigate

- Hook cho phép bạn `điều hướng` chương trình từ một `component`

```tsx
const navigate = useNavigate();

const goToHome = () => {
  navigate("/");
};
```

## useLocation

- Hook cho phép bạn truy cập thông tin về vị trí hiện tại của ứng dụng, bao gồm cả `pathname`, `search`, và `hash`.

```tsx
const LocationDisplay = () => {
  const { pathname, search, hash } = useLocation();
  return <div>Current path: {pathname}</div>;
};
```

## useSearchParams

- Hook để làm việc với query parameters

```tsx
const [searchParams, setSearchParams] = useSearchParams();

// Đọc: ?name=john&age=25
const name = searchParams.get("name");
const age = searchParams.get("age");

// Cập nhật
setSearchParams({ name: "jane", age: "30" });
```

## Outlet

- Render nested routes

```tsx
function Layout() {
  return (
    <div>
      <nav>Navigation</nav>
      <Outlet /> {/* Nested routes render ở đây */}
    </div>
  );
}

// Routes
<Route path="/" element={<Layout />}>
  <Route index element={<Home />} />
  <Route path="about" element={<About />} />
</Route>;
```

## Protected Routes

```tsx
function ProtectedRoute({ children }) {
  const { user } = useAuth();
  return user ? children : <Navigate to="/login" replace />;
}

// Sử dụng
<Route
  path="/dashboard"
  element={
    <ProtectedRoute>
      <Dashboard />
    </ProtectedRoute>
  }
/>;
```

## Lazy Loading

```tsx
import { lazy, Suspense } from "react";

const Dashboard = lazy(() => import("./Dashboard"));

<Route
  path="/dashboard"
  element={
    <Suspense fallback={<div>Loading...</div>}>
      <Dashboard />
    </Suspense>
  }
/>;
```

## DEMO Hoàn chỉnh

```tsx
import {
  BrowserRouter,
  Routes,
  Route,
  Link,
  NavLink,
  useParams,
  useNavigate,
  useSearchParams,
  Outlet,
} from "react-router-dom";

// Layout component
function Layout() {
  return (
    <div>
      <nav>
        <NavLink
          to="/"
          className={({ isActive }) => (isActive ? "active" : "")}
        >
          Home
        </NavLink>
        <NavLink to="/users">Users</NavLink>
        <NavLink to="/about">About</NavLink>
      </nav>
      <main>
        <Outlet />
      </main>
    </div>
  );
}

// Components
const Home = () => <h2>Home Page</h2>;
const About = () => <h2>About Page</h2>;

function Users() {
  return (
    <div>
      <h2>Users</h2>
      <Link to="/users/123">User 123</Link>
      <Outlet />
    </div>
  );
}

function UserDetail() {
  const { id } = useParams();
  const [searchParams] = useSearchParams();
  const tab = searchParams.get("tab") || "profile";

  return (
    <div>
      <h3>User {id}</h3>
      <p>Current tab: {tab}</p>
    </div>
  );
}

// App với nested routes
function App() {
  return (
    <BrowserRouter>
      <Routes>
        <Route path="/" element={<Layout />}>
          <Route index element={<Home />} />
          <Route path="about" element={<About />} />
          <Route path="users" element={<Users />}>
            <Route path=":id" element={<UserDetail />} />
          </Route>
        </Route>
      </Routes>
    </BrowserRouter>
  );
}

export default App;
```
