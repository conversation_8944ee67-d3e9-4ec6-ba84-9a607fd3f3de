# React Router DOM

## 1. BrowserRouter

- <PERSON><PERSON> bọc ứng dụng để cung cấp khả năng điều hướng
- Component cung cấp khả năng điều hướng sử dụng `lịch sử trình duy<PERSON>t`

```tsx
const Root = () => (
  <BrowserRouter>
    <App />
  </BrowserRouter>
);
```

## 2. Routes và Route

- `Routes` là một component chứa nhiều `Route`
- Mỗi `Route` định nghĩa một `tuyến đường` trong ứng dụng

```tsx
<Routes>
  <Route path="/" element={<Home />} />
  <Route path="/about" element={<About />} />
</Routes>
```

## 3. Link

- Thay vì sử dụng thẻ `<a>`, bạn sử dụng `<Link>` để điều hướng mà `không làm mới trang`

```tsx
<Link to="/about">About</Link>
<Link to="/users/123">User 123</Link>
```

## 4. NavLink

- C<PERSON> thể thêm các `class active`. Hữu ích cho việc tạo menu điều hướng với trạng thái "đang hoạt động"

```tsx
<NavLink to="/about" className={({ isActive }) => (isActive ? "active" : "")}>
  About
</NavLink>
```

---

## 5. useParams

- Hook cho phép bạn truy cập các `tham số` của `URL`

```tsx
// URL: /user/:id
const { id } = useParams();
console.log(id); // "123" nếu URL là /user/123
```

## 6. useNavigate

- Hook cho phép bạn `điều hướng` chương trình từ một `component`

```tsx
const navigate = useNavigate();

const goToHome = () => {
  navigate("/");
};

const goBack = () => {
  navigate(-1); // Quay lại trang trước
};
```

## 7. useLocation

- Hook cho phép bạn truy cập thông tin về vị trí hiện tại của ứng dụng

```tsx
const LocationDisplay = () => {
  const { pathname, search, hash, state } = useLocation();

  return (
    <div>
      <p>Path: {pathname}</p>
      <p>Search: {search}</p>
      <p>Hash: {hash}</p>
    </div>
  );
};
```

## 8. useSearchParams

- Hook để làm việc với query parameters

```tsx
const [searchParams, setSearchParams] = useSearchParams();

// Đọc: ?name=john&age=25
const name = searchParams.get("name");
const age = searchParams.get("age");

// Cập nhật
setSearchParams({ name: "jane", age: "30" });
```

----------------------------

## 9. Outlet

- Render nested routes trong layout components

```tsx
function Layout() {
  return (
    <div>
      <nav>Navigation</nav>
      <main>
        <Outlet /> {/* Nested routes render ở đây */}
      </main>
    </div>
  );
}

// Nested Routes
<Route path="/" element={<Layout />}>
  <Route index element={<Home />} />
  <Route path="about" element={<About />} />
  <Route path="users/:id" element={<UserDetail />} />
</Route>;
```

## 10. Protected Routes

- Bảo vệ routes yêu cầu authentication

```tsx
function ProtectedRoute({ children }) {
  const { user } = useAuth();
  return user ? children : <Navigate to="/login" replace />;
}

// Sử dụng
<Route
  path="/dashboard"
  element={
    <ProtectedRoute>
      <Dashboard />
    </ProtectedRoute>
  }
/>;
```

## 11. Lazy Loading Routes

- Code splitting để tối ưu performance

```tsx
import { lazy, Suspense } from "react";

const Dashboard = lazy(() => import("./Dashboard"));
const Profile = lazy(() => import("./Profile"));

<Route
  path="/dashboard"
  element={
    <Suspense fallback={<div>Loading Dashboard...</div>}>
      <Dashboard />
    </Suspense>
  }
/>;
```

---

## 12. DEMO Hoàn chỉnh

```tsx
import {
  BrowserRouter,
  Routes,
  Route,
  Link,
  NavLink,
  useParams,
  useNavigate,
  useSearchParams,
  Outlet,
} from "react-router-dom";

// Layout component
function Layout() {
  return (
    <div>
      <nav>
        <NavLink
          to="/"
          className={({ isActive }) => (isActive ? "active" : "")}
        >
          Home
        </NavLink>
        <NavLink to="/users">Users</NavLink>
        <NavLink to="/about">About</NavLink>
      </nav>
      <main>
        <Outlet />
      </main>
    </div>
  );
}

// Components
const Home = () => <h2>Home Page</h2>;
const About = () => <h2>About Page</h2>;

function Users() {
  return (
    <div>
      <h2>Users</h2>
      <Link to="/users/123">User 123</Link>
      <Outlet />
    </div>
  );
}

function UserDetail() {
  const { id } = useParams();
  const [searchParams] = useSearchParams();
  const tab = searchParams.get("tab") || "profile";

  return (
    <div>
      <h3>User {id}</h3>
      <p>Current tab: {tab}</p>
    </div>
  );
}

// App với nested routes
function App() {
  return (
    <BrowserRouter>
      <Routes>
        <Route path="/" element={<Layout />}>
          <Route index element={<Home />} />
          <Route path="about" element={<About />} />
          <Route path="users" element={<Users />}>
            <Route path=":id" element={<UserDetail />} />
          </Route>
        </Route>
      </Routes>
    </BrowserRouter>
  );
}

export default App;
```

---

## 13. Object-based Routes

- Định nghĩa routes bằng object thay vì JSX (React Router v6.4+)

```tsx
import { createBrowserRouter, RouterProvider } from "react-router-dom";

// Định nghĩa routes bằng object
const router = createBrowserRouter([
  {
    path: "/",
    element: <Layout />,
    errorElement: <ErrorPage />,
    children: [
      {
        index: true,
        element: <Home />,
      },
      {
        path: "about",
        element: <About />,
      },
      {
        path: "users",
        element: <Users />,
        children: [
          {
            path: ":id",
            element: <UserDetail />,
            loader: ({ params }) => {
              return fetch(`/api/users/${params.id}`);
            },
          },
        ],
      },
      {
        path: "dashboard",
        element: <Dashboard />,
        loader: async () => {
          // Data loading trước khi render
          const data = await fetch("/api/dashboard");
          return data.json();
        },
      },
    ],
  },
]);

function App() {
  return <RouterProvider router={router} />;
}
```

### Với Loader và Action

```tsx
const router = createBrowserRouter([
  {
    path: "/users/:id/edit",
    element: <EditUser />,
    // Load data trước khi render
    loader: async ({ params }) => {
      const user = await fetch(`/api/users/${params.id}`);
      return user.json();
    },
    // Handle form submission
    action: async ({ request, params }) => {
      const formData = await request.formData();
      const updates = Object.fromEntries(formData);

      await fetch(`/api/users/${params.id}`, {
        method: "PUT",
        body: JSON.stringify(updates),
        headers: { "Content-Type": "application/json" },
      });

      return redirect(`/users/${params.id}`);
    },
  },
]);

// Component sử dụng loader data
function EditUser() {
  const user = useLoaderData(); // Data từ loader
  const navigation = useNavigation(); // Loading state

  return (
    <Form method="post">
      <input name="name" defaultValue={user.name} />
      <input name="email" defaultValue={user.email} />
      <button type="submit" disabled={navigation.state === "submitting"}>
        {navigation.state === "submitting" ? "Saving..." : "Save"}
      </button>
    </Form>
  );
}
```

### Error Handling

```tsx
function ErrorPage() {
  const error = useRouteError();

  return (
    <div>
      <h1>Oops!</h1>
      <p>Sorry, an unexpected error has occurred.</p>
      <p>
        <i>{error.statusText || error.message}</i>
      </p>
    </div>
  );
}

const router = createBrowserRouter([
  {
    path: "/",
    element: <Layout />,
    errorElement: <ErrorPage />, // Global error boundary
    children: [
      {
        path: "users/:id",
        element: <UserDetail />,
        errorElement: <UserError />, // Specific error boundary
        loader: async ({ params }) => {
          const response = await fetch(`/api/users/${params.id}`);
          if (!response.ok) {
            throw new Response("User not found", { status: 404 });
          }
          return response.json();
        },
      },
    ],
  },
]);
```