# Routes

- Sử dụng `<router-outlet></router-outlet>` để là nới hiển thị các component router (Thường đặt ở component AppRoot)
- <PERSON><PERSON><PERSON><PERSON> hướng với routerLink `<a routerLink="/about">About</a>`
- Sử dụng `Service Router` để điều hướng
- Sử dụng `Service ActivatedRoute` để lấy ra các thành phần trong URL

```tsx
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { HomeComponent } from './home/<USER>';
import { AboutComponent } from './about/about.component';
import { ContactComponent } from './contact/contact.component';

const routes: Routes = [
  { path: '', component: HomeComponent }, // Route mặc định
  {
    path: 'admin',
    component: AdminComponent,
    children: [
      { path: 'settings', component: SettingsComponent },
      { path: 'users', component: UsersComponent }
    ]
  },
  { path: 'user/:id', component: UserDetailComponent },
  { path: '**', component: PageNotFoundComponent } // Route không tìm thấy
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { }
```

- Các HOOK
```tsx
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Router } from '@angular/router';

@Component({
  selector: 'app-user',
  templateUrl: './user.component.html',
  styleUrls: ['./user.component.css']
})
export class UserComponent implements OnInit {
  userId: string;

  constructor(
    private route: ActivatedRoute;
    private router: Router;
  ) {}

  ngOnInit(): void {
    // Lấy giá trị tham số id trên URL
    this.userId = this.route.snapshot.paramMap.get('id') || ''; 

    // Theo giõi thay đổi của params
    this.route.params.subscribe(params => {
      this.userId = params['id'];
    });
  }

  // Điều hướng đến route động
  navigateToUser(id: string) {
    this.router.navigate(['/user', id]); // Điều hướng đến 'user/:id'
  }
  
  // Điều hướng với tham số query
  navigateWithQuery() {
    this.router.navigate(['/product'], { queryParams: { category: 'books', id: 123 } });
  }
}
```
## Kiến thức thêm

1. Lazy Loading Module

```tsx
const routes: Routes = [
  {
    path: 'admin',
    loadChildren: () => import('./admin/admin.module').then(m => m.AdminModule)
  }
];
```

2.  Route Guards

- `CanActivate`, `CanDeactivate`, `Resolve`, `CanLoad`...


```tsx
{ path: 'admin', component: AdminComponent, canActivate: [AuthGuard] }

```