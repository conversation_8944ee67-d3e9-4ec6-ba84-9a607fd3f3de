# Tổng quan NestJS

## 1. Th<PERSON><PERSON> phần

| Th<PERSON><PERSON> phần         | Vai trò                               |
| ------------------ | ------------------------------------- |
| `Module`           | <PERSON>h<PERSON><PERSON> các thành phần có liên quan      |
| `Controller`       | Tiếp nhận và xử lý HTTP request       |
| `Service`          | Chứa logic nghiệp vụ                  |
| `Provider`         | <PERSON><PERSON> thể inject vào nơi khác            |
| `Entity`/`Schema`  | Cấu trúc dữ liệu ánh xạ DB            |
| `DTO`              | Kiểu dữ liệu đầu vào                  |
| `Pipe`             | Validate và transform dữ liệu         |
| `Guard`            | Kiểm tra quyền truy cập               |
| `Interceptor`      | Logging, caching, modify response     |
| `Middleware`       | Xử lý sớm nhất request (auth, log...) |
| `Exception Filter` | <PERSON><PERSON> lý lỗi                             |

## `Module` – Nhóm các thành phần có liên quan

```ts
@Module({
  controllers: [UserController],
  providers: [UserService],
})
export class UserModule {}
```

## `Controller` – Tiếp nhận và xử lý HTTP request

```ts
@Controller("users")
export class UserController {
  constructor(private userService: UserService) {}

  @Get()
  getAllUsers() {
    return this.userService.findAll();
  }
}
```

## `Service` – Chứa logic nghiệp vụ

```ts
@Injectable()
export class UserService {
  private users = [];

  getAllUsers() {
    return this.users;
  }
}
```

## `Provider` – Có thể inject vào nơi khác

```ts
@Injectable()
export class LoggerService {
  log(message: string) {
    console.log(message);
  }
}

// Sử dụng
constructor(private logger: LoggerService) {}
```

## `Entity` – Cấu trúc dữ liệu ánh xạ DB

### Với TypeORM

```ts
@Entity()
export class User {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;
}

// Sử dụng
TypeOrmModule.forFeature([User]);
```

## `DTO` – Kiểu dữ liệu đầu vào

- Kết hợp `class-validator` để validate input:

```ts
import { IsString, IsInt } from 'class-validator';

export class CreateUserDto {
  @IsString()
  name: string;

  @IsEmail()
  email: string;
}

// Sử dụng với pipe `ValidationPipe`
@Post()
create(@Body() dto: CreateUserDto) {
  return this.userService.create(dto);
}
```

## `Pipe` – Validate và transform dữ liệu

```ts
@Get(':id')
getUser(@Param('id', ParseIntPipe) id: number) {
  return this.userService.findById(id);
}
```

## `Guard` – Kiểm tra quyền truy cập

```ts
@Injectable()
export class RolesGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    // kiểm tra quyền người dùng
    return true;
  }
}

// Sử dụng với @UseGuards()
@UseGuards(AuthGuard)
@Get()
findAll() {
  return this.userService.findAll();
}
```

## `Interceptor` – Logging, caching, modify response

```ts
@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    console.log('Before...');
    return next.handle().pipe(tap(() => console.log('After...')));
  }
}

// Sử dụng với @UseInterceptors()
@UseInterceptors(LoggingInterceptor)
@Get()
getAllUsers() {
  return this.userService.findAll();
}
```

## `Middleware` – Xử lý sớm nhất request (auth, log...)

```ts
export class LoggerMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: Function) {
    console.log(`Request... ${req.method} ${req.url}`);
    next();
  }
}
```

## `Exception Filter` – Xử lý lỗi

- Mặc định có `HttpExceptionFilter`

```ts
throw new HttpException('Forbidden', HttpStatus.FORBIDDEN);

// Kết quả
{
  "statusCode": 403,
  "message": "Forbidden"
}
```

- Custom, sử dụng `@Catch()`

```ts
@Catch(NotFoundException)
export class NotFoundFilter implements ExceptionFilter {
  catch(exception: NotFoundException, host: ArgumentsHost) {
    // Chỉ chạy khi lỗi là NotFoundException
  }
}

// Sử dụng
@UseFilters(NotFoundFilter)
@Get()
getUser() {
  throw new Error('Something went wrong!');
}
```

## 2. Cấu trúc thư mục

```bash
src/
├── auth/
│   ├── auth.module.ts
│   ├── auth.controller.ts
│   ├── auth.service.ts
│   ├── strategies/
│   │   └── jwt.strategy.ts
│   └── dto/
│       ├── login.dto.ts
│       └── register.dto.ts
├── users/
│   ├── users.module.ts
│   ├── users.controller.ts
│   ├── users.service.ts
│   ├── entities/
│   │   └── user.entity.ts
│   └── dto/
│       ├── create-user.dto.ts
│       └── update-user.dto.ts
├── common/
│   ├── guards/
│   │   ├── auth.guard.ts
│   │   └── roles.guard.ts
│   ├── pipes/
│   │   └── validation.pipe.ts
│   ├── interceptors/
│   │   └── logging.interceptor.ts
│   ├── decorators/
│   │   ├── roles.decorator.ts
│   │   └── get-user.decorator.ts
│   └── filters/
│       └── http-exception.filter.ts
├── config/
│   └── configuration.ts
├── app.module.ts
└── main.ts
```

## 3. Thư viện phổ biến

- `@nestjs/typeorm`, `@nestjs/prisma` – ORM
- `class-validator`, `class-transformer` – validate dữ liệu
- `@nestjs/passport`, `passport-jwt` – xác thực
- `@nestjs/swagger` – API docs
- `dotenv` – biến môi trường

---

## 4. Mô hình kiến trúc có thể dùng

| Mô hình | Mô tả                     |
| ------- | ------------------------- |
| MVC     | Model - View - Controller |
