# Form ReactJS

Có hai cách xử lý form trong React, chúng khác nhau chủ yếu trong cách quản lý dữ liệu:

## 1. uncontrollerd components

- Nếu dữ liệu được xử lý bởi `DOM`, g<PERSON><PERSON> là `uncontrollerd components` (`<input type="file">`)
- <PERSON><PERSON><PERSON> trị của `Form` lấy trực tiếp từ `DOM`

## 2. controlled components

- Nếu dữ liệu được xử lý bởi `components`, gọi là `controlled componens`
- Giá trị của `Form` được quản lý bởi State, Store và truyền vào `Form`. Lấy giá trị lưu vào State bởi hàm `onChange`
- Giúp `Validate Input` trước hoặc sau khi gán giá trị
