# Form ReactJS

Có hai cách xử lý form trong React, chúng khác nhau chủ yếu trong cách quản lý dữ liệu `(state)`:

## 1. Uncontrollerd components

- Dữ liệu được xử lý bởi `DOM`
- <PERSON><PERSON><PERSON> dữ liệu qua `ref`
- <PERSON><PERSON> dụ: `<input type="file">`  
✔️ Hiệu năng tốt

## 2. Controlled components

- Dữ liệu được xử lý bởi `React components`
- Sử dụng `state` để lưu giá trị input và cập nhật thông qua sự kiện `onChange`
- Giúp `Validate Input` trước hoặc sau khi gán giá trị
❌ Nhiều state
❌ Hiệu năng không tốt khi state thay đổi nhiều
