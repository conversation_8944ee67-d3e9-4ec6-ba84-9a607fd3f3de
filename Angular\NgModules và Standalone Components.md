# NgModules & Standalone Components trong Angular

## 1. NgModules là gì?

- Là **đơn vị tổ chức** trong Angular: gom component, directive, service, pipe,...
- Mỗi ứng dụng Angular luôn có ít nhất 1 module: `AppModule`.

### Cấu trúc cơ bản

```ts
@NgModule({
  declarations: [AppComponent, UserComponent],
  imports: [BrowserModule, FormsModule],
  providers: [UserService],
  bootstrap: [AppComponent]
})
export class AppModule {}
```

---

## 2. Vai trò của NgModule

| Thành phần     | Ý nghĩa |
|----------------|--------|
| `declarations` | Khai báo component, directive, pipe |
| `imports`      | Nhúng các module khác |
| `providers`    | Khai báo service dùng trong DI |
| `bootstrap`    | Component gốc khởi chạy ứng dụng |

---

## 3. Standalone Components (Angular 14+)

- <PERSON>hông cần khai báo trong `NgModule`
- <PERSON><PERSON> tách biệt, tái sử dụng, dùng cho micro frontend
- Giúp giảm phụ thuộc và dễ lazy-load

### Khai báo Standalone Component

```ts
@Component({
  standalone: true,
  selector: 'app-user',
  templateUrl: './user.component.html',
  imports: [CommonModule, FormsModule]
})
export class UserComponent {}
```

### Dùng Standalone trong Router

```ts
const routes: Routes = [
  { path: 'user', component: UserComponent } // không cần module
];
```

---

## 4. So sánh NgModule vs Standalone

| Tiêu chí                    | NgModule                          | Standalone Component              |
|-----------------------------|------------------------------------|-----------------------------------|
| Cần khai báo trong module   | ✅ Có                              | ❌ Không cần                       |
| Tính phụ thuộc              | Nhiều hơn                         | Ít hơn                            |
| Dễ tái sử dụng              | Trung bình                        | ✅ Rất dễ (độc lập)                |
| Hỗ trợ micro frontend       | ❌ Khó triển khai                  | ✅ Phù hợp                         |
| Sử dụng từ Angular phiên bản| Từ đầu                            | Angular 14 trở lên                |

---

## 5. Khi nào dùng cái nào?

| Trường hợp                              | Nên dùng gì            |
|-----------------------------------------|-------------------------|
| App lớn, dùng từ Angular cũ             | NgModule                |
| Component đơn, module con đơn giản      | Standalone             |
| Micro frontend, lazy-load tốt hơn       | Standalone             |
| Codebase cũ, chưa nâng cấp              | NgModule                |

---

## 6. Kết hợp cả hai

- Bạn có thể dùng Standalone component **trong NgModule**
- Hoặc dùng `import()` để lazy load Standalone component

```ts
@NgModule({
  imports: [UserComponent] // dùng standalone như imports
})
export class SomeModule {}
```

---

## 7. Tổng kết

| Kiến thức             | Ghi nhớ                                      |
|-----------------------|-----------------------------------------------|
| NgModule              | Truyền thống, cần declarations/imports        |
| Standalone Component  | Nhẹ, không cần module, tối ưu lazy loading    |
| Angular hỗ trợ        | Angular 14+                                   |