# Kết nối SQL Database bằng TypeORM

- Cài đặt:

```bash
npm install --save @nestjs/typeorm typeorm mysql2
```
- <PERSON><PERSON><PERSON> hình:

```tsx
// app.module.ts
import { TypeOrmModule } from '@nestjs/typeorm';

@Module({
  imports: [
    TypeOrmModule.forRoot({
      type: 'mysql',
      host: 'localhost',
      port: 3306,
      username: 'root',
      password: '123456',
      database: 'nestdb',
      entities: [__dirname + '/**/*.entity{.ts,.js}'], // Nơi chứa entity
      synchronize: true, // Chỉ dùng cho dev (tự động tạo bảng từ entity môi lần chạy <PERSON>ng dụng)
    }),
  ],
})
export class AppModule {}

```