# Async / Await (ES7)

## Async

- <PERSON><PERSON> báo một hàm bất đồng bộ
- Tự động trả về một `Promise`

```js
async function fetchData() {
  return "Hello"; // return về Promise.resolve("Hello")
}
```

## Await

- Chỉ dùng bên trong hàm `async`
- Dùng để chờ kết quả của một `Promise`
- Tạm dừng thực thi cho đến khi Promise hoàn thành

```js
async function getJSONAsync() {
  let data = await axios.get(
    "https://tutorialzine.com/misc/files/example.json"
  );

  return data.data;
}

getJSONAsync().then((res) => console.log(res));
```

## Xử lý lỗi trong Async / Await

- Sử dụng `try / catch`

```js
async function doSomethingAsync() {
  try {
    let result = await someAsyncCall();
  } catch (error) {
    // Bắt tất cả các lỗi xảy ra trong khối try()
  }
}
```
