# ES6 (ECMAScript 2015)

## 1. `Block Scope`, `let`, `const`, `class contructor`

- <PERSON><PERSON><PERSON> biến được khai báo trong 1 Scope thì biến đó chỉ sử dụng được trong Scope đó

## 2. Arrow Functions

- <PERSON>h<PERSON>ng có `context` riêng (Sử dụng `context` của hàm gần nhất chứa nó) => `this` của Arrow Functions lấy `this` phạm vị bên ngoài gần nhất chứa nó
- Không thể được sử dụng như là constructor để tạo ra các đối tượng mới.

## 3. Default Parameter Values

```js
function addNewStudent(name = "<PERSON>uang Anh", age) {
  return { name, age };
}
```

## 4. Promise

## 5. Destructuring và toán tử `...`

```ts
var { firstName, lastName } = obj;
```

## 6. Template String

```js
console.log(`<PERSON><PERSON> chao ${name}`);
```

## 7. Multi-line Strings

## 8. Map và Set

- `Map`: <PERSON><PERSON><PERSON> dữ liệu cấu trúc `key-value`, `key` là `unique`

```ts
let myMap = new Map();

myMap.set("key1", "value1");
myMap.set("key2", "value2");

console.log(myMap.get("key1")); // output: "value1"
console.log(myMap.has("key2")); // output: true
myMap.delete("key1");
console.log(myMap.size); // output: 1
```

- `Set`: Lưu dữ liệu không trung lặp

```ts
let mySet = new Set();

mySet.add(1);
mySet.add(2);
mySet.add(2); // Giá trị 2 sẽ không được thêm vào do Set không chấp nhận giá trị trùng lặp

console.log(mySet.size); // output: 2
mySet.delete(2);
```
