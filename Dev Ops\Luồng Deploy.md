# Deploy dự án

1. <PERSON><PERSON> `<PERSON><PERSON><PERSON>`
2. <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> ảo VPS` chạy `Linux/Ubuntu`
3. <PERSON><PERSON>i NodeJS, PM2, Nginx, Git, MongoDB, Docker
4. Pull code từ `Github` về VPS `(<PERSON><PERSON><PERSON> <PERSON>ù<PERSON>er, Pull Image từ DockerHub về VPS)`
5. Build và chạy code `(<PERSON><PERSON><PERSON> <PERSON>ù<PERSON> Docker, Run Container từ Image)`
6. <PERSON><PERSON><PERSON> <PERSON>ì<PERSON> `<PERSON>inx` để trỏ `Domain`, `HTTPS/SSL`, `HTTP2`
