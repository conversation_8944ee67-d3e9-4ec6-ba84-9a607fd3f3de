# Điều cần biết về OOP

## Các đặc trưng

### 1. T<PERSON><PERSON> kế thừa (Inheritance)

- `Class`: Sử dụng `extends`
- `Function Contructor`: Sử dụng `.call()` hoặc `prototype`
- <PERSON><PERSON> dụ `Function Contructor`
```ts
// muốn `SuperMan` sẽ kế thừa các thuộc tính của `Person`
SuperMan.prototype = new Person();
```

### 2. T<PERSON>h đóng gói (Encapsulation)

- `Class`: Sử dụng biến `private` hoặc `#` hoặc `_` cùng với `Getter` `Setter`

```tsx
// ES6 sử dụng `_` hoặc `private`
this._age = age;
private age = age;

// ES2020 sử dụng `#`
#age;
```

- `Function Contructor`: Sử dụng `biến cục bộ` và `closure`

```tsx
var age;
```

### 3. <PERSON><PERSON><PERSON> đa hình (Polymorphism )

- <PERSON><PERSON><PERSON> đơn giản là một `phương thức` cùng tên có thể thực hiện các hành vi khác nhau tùy thuộc vào `Class` của `đối tượng` gọi `phương thức` đó.
- Bằng cách `ghi đè` phương thức của lớp cha khi kế thừa

```tsx
class Shape {
  draw() {
    console.log("Drawing a shape");
  }
}

// Kế thừa và ghi đè draw()
class Circle extends Shape {
  draw() {
    console.log("Drawing a circle");
  }
}

// Kế thừa và ghi đè draw()
class Rectangle extends Shape {
  draw() {
    console.log("Drawing a rectangle");
  }
}

let circle = new Circle();
let rectangle = new Rectangle();

// Cùng gọi phương thức `draw()` nhưng mỗi Class xử lý khác nhau
circle.draw(); // Output: Drawing a circle
rectangle.draw(); // Output: Drawing a rectangle
```

### 4. Tính trừu tượng (Abstraction)

- Định nghĩa `phương thức` không có triển khai cụ thể trong lớp `cha` hoặc `ném một ngoại lệ` để báo cho `lớp con` biết rằng `phương thức` phải được `ghi đè`.

```tsx
class Shape {
  draw() {
    throw new Error("Method 'draw' must be implemented");
  }
}

class Circle extends Shape {
  draw() {
    console.log("Drawing a circle");
  }
}

let circle = new Circle();
circle.draw(); // Output: Drawing a circle
```

## Prototype

- `Prototype` chính là cha của 1 `Object`
- Mỗi 1 đối tượng đều có thuộc tính `prototype` dù khởi tạo 1 object rỗng
- Dùng để thêm `phương thức` cho `funtion contractor`
- Tất cả các phương thức đều được thêm vào `prototype`
- khi kế thừa sẽ kế thừa `prototype`

```ts
function Person() {}

// Thêm property và method cho object Person
Person.prototype.firstName = "Hoang";
Person.prototype.showName = function () {
  console.log(this.firstName + " " + this.lastName);
};
```
