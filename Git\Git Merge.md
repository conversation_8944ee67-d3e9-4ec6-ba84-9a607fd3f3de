# Git merge (<PERSON><PERSON><PERSON> nhất code)

- Thường sử dụng `git merge` hợp nhất code từ nhánh `feature` v<PERSON>o nh<PERSON>h `master`

```js
// Tại nhánh `master`
git merge feature
// Hoặc
git pull feature
```

## Nguyên lý

- Tạo 1 commit `merge` chứa thay đổi của `nh<PERSON>h hiện tại` sau khi hợp nhất nhánh và đưa nó lên đầu

  _**Minh họa**_
  ![alt](https://nhobethoi.com/wp-content/uploads/2021/06/git-merge-gop-cac-nhanh-thanh-nhanh-duy-nhat-1.png)

- Tại nhánh `feature`: `git merge master`
  ![alt](https://nhobethoi.com/wp-content/uploads/2021/06/git-merge-gop-cac-nhanh-thanh-nhanh-duy-nhat-2-1.png)

## Ưu nhược

- Bi<PERSON><PERSON> được thời điểm `merge` khi nà<PERSON> nhờ commit `merge`
- Không thay đổi lịch sử commit
- T<PERSON>o ra cấu trúc `commit` kim cương
- Commit được gom hết lại để merge => `Xử lý Conflict khó khăn`

## Xử lý merge bị conflic

- Fix conflict
- `git add .`
- `git commit --no-edit`
- `git push`
