# Observable vs Promise trong JavaScript / Angular

## 1. Giống nhau

| Đặc điểm                   | Promise       | Observable    |
|----------------------------|---------------|---------------|
| Xử lý bất đồng bộ         | ✅            | ✅            |
| <PERSON><PERSON> thể trả về giá trị sau | ✅            | ✅            |
| <PERSON><PERSON> thể xử lý lỗi          | ✅ `.catch()` | ✅ `.catchError()` hoặc `.subscribe(error)` |

---

## 2. Kh<PERSON>c nhau

| Tiêu chí                   | Promise                         | Observable                            |
|----------------------------|----------------------------------|----------------------------------------|
| Đa giá trị                 | ❌ Chỉ 1 giá trị duy nhất         | ✅ Nhiều giá trị (stream)              |
| Lazy (chậm thực thi)      | ✅                               | ✅                                     |
| Cancel (hủy giữa chừng)   | ❌ Không                         | ✅ Có thể hủy (unsubscribe)            |
| Tích hợp RxJS             | ❌ Không                         | ✅ Có (map, filter, debounceTime,...)  |
| Dễ test, mở rộng          | Trung bình                       | ✅ Dễ mở rộng, dùng pipe hiệu quả     |
| Cú pháp xử lý             | `.then().catch()`                | `.subscribe()`, `.pipe()`             |

---

## 3. Ví dụ Promise

```ts
const promise = new Promise((resolve, reject) => {
  setTimeout(() => resolve('Done'), 1000);
});

promise.then(data => console.log(data));
```

---

## 4. Ví dụ Observable

```ts
import { Observable } from 'rxjs';

const observable = new Observable(observer => {
  observer.next('First');
  setTimeout(() => {
    observer.next('Second');
    observer.complete();
  }, 1000);
});

observable.subscribe({
  next: (data) => console.log(data),
  complete: () => console.log('Done')
});
```

---

## 5. Hủy Observable

```ts
const subscription = observable.subscribe(...);

// Hủy sau 1.5s
setTimeout(() => {
  subscription.unsubscribe();
}, 1500);
```

---

## 6. Khi nào dùng cái nào?

| Trường hợp                                 | Nên dùng       |
|--------------------------------------------|----------------|
| Gọi API đơn, trả về 1 lần duy nhất         | Promise        |
| Gọi API nhiều lần, polling, long-lived     | Observable     |
| Theo dõi sự kiện (event, socket, stream)   | Observable     |
| Đơn giản, không cần hủy                     | Promise        |

---

## 7. Tổng kết

- **Promise**: đơn giản, thích hợp với tác vụ **một lần**.
- **Observable**: mạnh mẽ hơn, dùng cho **dòng dữ liệu**, nhiều thao tác phức tạp và **có thể hủy**.