# ref, useRef, forwordRef

## I. useRef

- `useRef` là 1 hook lưu trữ biến có thể `mutate` (không làm thay đổi tham chiếu mỗi re-render)
- Truy cập trực tiếp tới `DOM`

### 1. `useRef` cho khai báo biến

```js
function reRender() {
  const intervalRef = useRef(null); // Không bị reset sau mỗi render
  
  useEffect(() => {
    intervalRef.current = setInterval(() => {
      console.log("Text");
    }, 1000);

    return () => {
      clearInterval(intervalRef.current);
    };
  });
}
```

### 2. `useRef` truy cập `DOM thật`

-Thay đổi DOM Không thông qua `DOM ảo` nên nhanh hơn

```js
function Component() {
  const textInput = useRef<HTMLElement>(null);

  const handleClick = () => {
    textInput.current.focus()
  }

  return (
    <input type='text' ref={textInput}/>
    <button onClick={handleClick}>CLick</button>
  )
```

## II. forwordRef (HOC)

- `ref` không phải prop nên không truyền vào component qua props
- Dùng `React.forwardRef` để cho phép component `con` nhận `ref` từ component `cha`, trong tham số thứ 2

```js
// Nhận ref là tham số thứ 2 truyền từ ngoài vào
const CustomHeader = React.forwordRef((props, ref) => {
  return <h1 ref={ref}>{props.text}</h1>;
});

// Sử dụng
function ParentComponent() {
  const customRef = useRef<HTMLElement>(null);

  handleClick = () => {
    // Lấy được DOM thẻ h1 trong compoent `CustomHeader`
    console.log(customRef.current)
  }

  return (
    <CustomHeader ref={customRef}>
    <button onClick={handleClick}>CLick</button>
  )
```
