# ref, useRef, forwordRef

## I. useRef

`useRef` là 1 hook lưu trữ biến có thể `mutate` (không làm thay đổi tham chiếu) hoặc cho phép truy cập `DOM`

### 1. `useRef` cho khai báo biến

```js
function reRender() {
  // useRef() không bị reset mỗi khi component reRender
  // Nói cách khác, useRef (mutate) không thay đổi tham chiếu
  const intervalRef = useRef(null);
  useEffect(() => {
    intervalRef.current = setInterval(() => {
      console.log("Text");
    }, 1000);
    return () => {
      clearInterval(intervalRef.current);
    };
  });
}
```

### 2. `useRef` truy cập `DOM thật`

-Thay đổi DOM Không thông qua `DOM ảo` nên nhanh hơn

```js
function Component() {
  const textInput = useRef<HTMLElement>(null);

  const handleClick = () => {
    textInput.current.focus()
  }

  return (
    <input type='text' ref={textInput}/>
    <button onClick={handleClick}>CLick</button>
  )
```

## II. forwordRef (HOC)

- `ref` không phải prop nên không truyền vào component qua props
- Nếu muốn nhận `ref` truyền từ ngoại vào component cần dùng `forwordRef` để nhận ref trong tham số thứ 2

```js
// Nhận ref là tham số thứ 2 truyền từ ngoài vào
const CustomHeader = React.forwordRef((props, ref) => {
  const { text } = props;
  return <h1 ref={ref}>{props.text}</h1>;
});

// Sử dụng
function Component() {
  const customRef = useRef<HTMLElement>(null);

  handleClick = () => {
    // Lấy được DOM thẻ h1 trong compoent `CustomHeader`
    console.log(customRef.current)
  }

  return (
    <CustomHeader ref={customRef}>
    <button onClick={handleClick}>CLick</button>
  )
```
