# T<PERSON>y cập nút DOM

`document` là root của mọi nút DOM

## Nút root

- `document.documentElement` - <PERSON><PERSON><PERSON> cập `HTML`
- `document.head` - <PERSON><PERSON><PERSON> cập `head`
- `document.body` - <PERSON><PERSON><PERSON> cập `body`

## Nút cha

- parentNode - Lấy nút cha
- parentElement - phần tử cha

- `document.body` - <PERSON><PERSON><PERSON> cập `body`
- `document.body` - T<PERSON><PERSON> cập `body`

```js
const html = document.documentElement;

console.log(html.parentNode); // > #document
console.log(html.parentElement); // > null
```

## Nút con

- `childNodes` - Tất cả nút con (chứa c<PERSON> `text`)
- `firstChild` - Nút con đầu tiên (tính cả `text`)
- `lastChild` - Nút con cuối cùng (tính cả `text`)
- `children` - N<PERSON>t con phần tử (<PERSON>h<PERSON><PERSON> tính `text`)
- `firstElementChild` - <PERSON><PERSON><PERSON> phần tử con đầu tiên (<PERSON><PERSON><PERSON>ng t<PERSON>h `text`)
- `lastElementChild` - <PERSON><PERSON><PERSON> phần tử con cuối cùng (Không tính `text`)

## Nút anh chị

- `previousSibling` - Nút anh chị em trước
- `nextSibling` - Nút anh chị em tiếp theo
- `previousElementSibling` - Nút phần tử anh chị em trước
- `nextElementSibling` - Nút phần tử anh chị em tiếp theo

## Vòng lặp

```js
for (let element of p.childNodes) {
  console.log(element);
}
```
