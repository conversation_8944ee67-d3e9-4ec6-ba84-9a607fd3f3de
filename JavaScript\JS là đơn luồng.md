# JavaScript là đơn luồng (Single-thread)

- Trong 1 thời điểm chỉ thực hiện 1 công việc

`https://anonystick.com/blog-developer/javascript-su-dung-don-luong-ly-do-tai-sao-2021040171820607`

## Call Stack và Even Loop

`https://viblo.asia/p/javascript-event-loop-va-call-stack-djeZ1zvYlWz`

- <PERSON><PERSON> thể khi thực thi `Bất đông bộ` sẽ hoạt động như sau:

1. <PERSON><PERSON><PERSON> hàm thực thi được gọi sẽ được đưa vào `Call Stack` `(FILO)`
2. Mỗi hàm chạy xong sẽ bị loại bỏ khỏi `Call Stack`
3. Khi có hàm bất đông bộ, nó sẽ được đưa vào Web APIs để thực thi và loại bỏ khỏi `Call Stack`
4. <PERSON><PERSON> hàm bất đông bộ thực thi xong thì sẽ đưa vào `CallBack Queuse` `(FIFO)`
5. <PERSON><PERSON> <PERSON><PERSON> `Call Stack` không còn tác vụ nào cần thực thi. Th<PERSON> hàm trong `CallBack Queuse` sẽ được đẩy vào `Call Stack` thông qua `Event Loop`

![alt](https://images.viblo.asia/1ba10877-9396-482c-a429-cd449ec1c82e.png)
