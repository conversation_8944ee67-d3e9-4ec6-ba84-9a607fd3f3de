# Các phương pháp sử dụng với Webpack

## Code Splitting

- Chia ứng dụng thành các phần nhỏ có thể tải độc lập, nhằm cải thiện hiệu suất và tốc độ tải trang

### Entry Points

- Chia mã nguồn thành nhiều entry points và tạo nhiều bundle. Điều này có thể cấu hình trong file webpack.config.js.

```js
module.exports = {
  entry: {
    home: "./src/home.js",
    about: "./src/about.js",
  },
  output: {
    filename: "[name].bundle.js",
    path: path.resolve(__dirname, "dist"),
  },
};
```

### Dynamic Imports

- Sử dụng cú pháp `import()` để tải module khi cần thiết

```js
function loadComponent() {
  import("./myComponent.js")
    .then((module) => {
      const myComponent = module.default;
      // Sử dụng myComponent
    })
    .catch((err) => {
      console.error("Failed to load the component", err);
    });
}
```

### SplitChunksPlugin

- Plugin này cho phép chia sẻ các module chung giữa các bundle. Điều này giúp tránh việc tải lại cùng một module nhiều lần.

```js
module.exports = {
  optimization: {
    splitChunks: {
      chunks: "all",
    },
  },
};
```

## Xử lý css, ảnh, font chữ, plugin

- Css: css-loader style-loader
- Ảnh, font chữ: file-loader, url-loader (Chuyển về dạng base64 URIs)

```js
const path = require("path");

module.exports = {
  entry: "./src/index.js",
  output: {
    filename: "bundle.js",
    path: path.resolve(__dirname, "dist"),
  },
  module: {
    rules: [
      {
        test: /\.(woff|woff2|eot|ttf|otf)$/i,
        type: "asset/resource",
      },
    ],
  },
};
```
