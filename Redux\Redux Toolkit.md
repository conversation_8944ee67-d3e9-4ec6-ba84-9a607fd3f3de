# Redux Toolkit

- `configureStore()`: <PERSON><PERSON><PERSON>ình global store của ứng dụng.
- `createAction()`: Tạo ra các action creators đơn giản hơn.
- `createReducer()`: Tạo reducers đơn giản hơn.
- `createSlice()`: Tạo ra các action creator và reducer dựa trên một slice (phần) của state.
- `createAsyncThunk()`: Tạo ra các async thunks trong Redux Toolkit.
- `createEntityAdapter()`: <PERSON>àm này được sử dụng để xử lý dữ liệu thực thể (entity data) trong Redux Toolkit.

## I. Khởi tạo `Actions` và `Reducers`

### Cách 1: Sử dụng `createSlice`

- createSlice giúp tạo Reducers và tự động render type Action

```js
import { createSlice } from "@reduxjs/toolkit";

const initialState = { value: 0 };

const calculatorSlice = createSlice({
  name: "counter", // Tiền tố
  initialState: initialState,
  // reducers: <PERSON><PERSON> lý các hành động cụ thể như `addCase`
  reducers: {
    // 'increment' : Tên action
    increment: (state, action) => {
      state.value += action.payload;
    },
    decrement: (state, action) => {
      state.value -= action.payload;
    },
  },

  // extraReducers: Xử lý các TH giống `createReducer`
  extraReducers: (builder) => {
    builder
      // `addCase`: Xử lý các Action cụ thể đã tạo
      .addCase(incrementBy, (state, action) => {})
      // `addMatcher`: Xử lý các Action Match với điều kiện Action.type
      .addMatcher(isActionWithNumberPayload, (state, action) => {})
      // `addDefaultCase`: Xử lý các Action Không thỏa mãn Action nào cả
      .addDefaultCase((state, action) => {});
  },
});

export const { increment, decrement } = calculatorSlice.actions;
export default calculatorSlice.reducer;
```

### Cách 2: Sử dụng `counterReducer` và `createAction`

```js
import { createAction, createReducer } from "@reduxjs/toolkit";

// Khởi tạo Action
const increment = createAction("counter/increment"); // counter/increment: là type
const decrement = createAction("counter/decrement");

const initialState = { value: 0 };

const counterReducer = createReducer(initialState, (builder) => {
  builder
    // `addCase`: Xử lý các Action cụ thể đã tạo
    .addCase(increment, (state, action) => {
      state.value += action.payload;
    })
    .addCase(decrement, (state, action) => {
      state.value -= action.payload;
    });
    // `addMatcher`: Xử lý các Action Match với điều kiện Action.type
    .addMatcher(isActionWithNumberPayload, (state, action) => {})
    // `addDefaultCase`: Xử lý các Action Không thỏa mãn Action nào cả
    .addDefaultCase((state, action) => {})
});
```

## II. Khởi tạo Store

- Sử dụng configureStore để khởi tạo Store và cung cấp cho App

```js
import { configureStore } from "@reduxjs/toolkit";
import { Provider } from "react-redux";
import Calculator from "./Calculator";
import calculatorReducer from "./CalculatorSlice";

// Khởi tạo Store
const store = configureStore({
  // reducer: truyền  các reducer vào object
  reducer: {
    calculator: calculatorReducer, // calculator: ten khi gọi state
  },
});

function App() {
  return (
    // Cung cấp Store cho App
    <Provider store={store}>
      <Calculator />
    </Provider>
  );
}

export default App;
```

## III. Sử dụng useDispatch và useSelector

- Dispatch trạng thái đến Store và getData từ Store

```js
const dispatch = useDispatch();
const value = useSelector((state) => state.calculator.value);

dispatch.increment(5);
```

## IV. Sử dụng `createAsyncThunk`

![alt](https://redux.js.org/assets/images/ReduxAsyncDataFlowDiagram-d97ff38a0f4da0f327163170ccc13e80.gif)

- Tạo Action bất đồng bộ và xử lý với `createAsyncThunk`
- Xử lý các trạng thái `pending`, `fulfilled`, `rejected`
- Với `cresteSlide` cần sử dụng trong `extraReducers` với `addCase`
- Ví dụ sử dụng với `createReducer` (`cresteSlide` tương tự):

```js
// Tạo `Action` bất đồng bộ `fetchUserById`
const fetchUserById = createAsyncThunk(
  "users/fetchByIdStatus",
  async (userId: number, thunkAPI) => {
    const response = await userAPI.fetchById(userId);
    return response.data;
  }
);

// Tạo `createReducer`
const counterReducer = createReducer(initialState, (builder) => {
  builder
    // Xử lý khi fetchUserById trả về trạng thái fulfilled
    .addCase(fetchUserById.fulfilled, (state, action) => {
      // Add user to the state array
      state.entities.push(action.payload);
    });
});

// Sử dụng
const { status, data, error } = useSelector((state) => state.data);
const dispatch = useDispatch();

useEffect(() => {
  // Dispatch action `fetchUserById`
  dispatch(fetchUserById());
}, [dispatch]);
```
