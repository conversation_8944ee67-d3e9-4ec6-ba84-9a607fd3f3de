# Observer (Chỉ lắng nghe giá trị)

- `Observer` là một đối tượng chứa các `callback` để xử lý các giá trị được phát ra bởi `observable`. <PERSON><PERSON> bao gồm 3 `phương thức`:

```text
- next(value): <PERSON><PERSON> lý một giá trị mới.
- error(err): X<PERSON> lý lỗi xảy ra.
- complete(): Xử lý khi chuỗi các giá trị kết thúc.
```

## Ví dụ

```ts
import { Observable } from "rxjs";

// Tạo một Observable đơn giản
const observable = new Observable((subscriber) => {
  subscriber.next("Hello");
  subscriber.next("World");
  subscriber.complete();
});

// Tạo một Observer
const observer = {
  next: (x) => console.log("Received value: " + x),
  error: (err) => console.error("Something went wrong: " + err),
  complete: () => console.log("Done"),
};

// Đăng ký Observer để nhận dữ liệu từ Observable
observable.subscribe(observer);
```
