# Yup

- <PERSON><PERSON> biến với React và Formik
- Thêm kiểm tra tùy chỉnh với `test`

```tsx
import * as Yup from "yup";

const UserSchema = Yup.object().shape({
  name: Yup.string().required(),
  age: Yup.number()
    .min(0)
    .max(120)
    .test(
      "is-valid-age",
      "Age must be between 0 and 120",
      (value) => value >= 0 && value <= 120
    )
    .required(),
});

const userData = { name: "<PERSON>", age: 30 };

// Xử lý lỗi với `validate`
UserSchema.validate(userData)
  .then((parsedData) => console.log(parsedData))
  .catch((err) => console.log(err.errors));

// hoặc `validateSync`
try {
  const parsedData = UserSchema.validateSync(validData);
  console.log(parsedData);
} catch (err) {
  console.error("Lỗi:", err.errors);
}
```

## Chức năng xác thực

- `validateSync`: <PERSON><PERSON> lý dữ liệu đồng bộ, tự bắn ra lỗi nếu có. <PERSON><PERSON><PERSON> kết hợp với `try catch`
- `validate`: <PERSON><PERSON> lý dữ liệu bất đồng bộ. Sử dụng `.then() .catch()` bắt lỗi
