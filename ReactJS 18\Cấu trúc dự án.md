# Cấu trúc dự án

```text
my-react-app/
    ├── node_modules/    # Thư mục chứa các module đã cài đặt
    ├── public/          # Thư mục này chứa các tệp tĩnh mà sẽ được trình duyệt tải xuống trực tiếp
    │   ├── index.html   # Tệp HTML mẫu
    │   ├── favicon.ico  # Biểu tượng trang web
    ├── src/             # Thư mục chứa mã nguồn của ứng dụng
    │   ├── components/  # Thư mục này chứa các thành phần React của ứng dụng
    │   │   ├── Header.js
    │   │   ├── Footer.js
    │   │   ├── ...
    │   ├── pages/       # Thư mục này chứa các trang của ứng dụng
    │   │   ├── Home.js
    │   │   ├── About.js
    │   │   ├── ...
    │   ├── services/    # Thư mục này chứa các dịch vụ để tương tác với API hoặc xử lý logic nghiệp vụ
    │   │   ├── api.js
    │   │   ├── auth.js
    │   │   ├── ...
    │   ├── styles/      # Thư mục này chứa các tệp CSS hoặc SCSS để định dạng ứng dụng
    │   │   ├── main.css
    │   │   ├── variables.scss
    │   │   ├── ...
    │   ├── App.js       # Component gốc của ứng dụng
    │   └── index.js     # File khởi đầu của ứng dụng
    ├── .gitignore       # Tệp để xác định các tệp không nên được git theo dõi
    ├── package.json     # Tệp cấu hình npm
    └── README.md        # Tệp chứa thông tin về dự án
```
