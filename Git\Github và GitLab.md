# Github và Gitlab

- <PERSON><PERSON> hệ thống quản lý phiên bản code

## C<PERSON><PERSON> khu vực làm việc với Git

- `<PERSON>hu vực làm việc (working tree)`: Ch<PERSON>h là nơi chúng ta đang code (vẫn ở local)
- `Staging`: Sau khi dùng `git add` thì file sẽ được đưa lên khu vực này (vẫn ở trên `local`)
- `Committed`: Sau khi dùng `git commit` thì file từ `Staging` sẽ được đưa lên đây (vẫn ở trên `local`)
- `Remote (Origin)`: Sau khi dùng `git push` thì file từ `commited` (file đã lên `server`)

## Sự khác nhau giữa `git reset, git restore, git revert`

- `git reset` chuyên reset `con trỏ HEAD` về một trạng thái nhất định
- `git restore` chuyên khôi phục những file trong working tree
- `git revert` chuyên khôi phục những commit nhưng sẽ tạo ra một commit revert.

## Git Flow

- `master`: Nơi chứa source chính để `release` sản phẩm ra cộng đồng.

- `hotfixes`: Được checkout từ `master`, để sửa nhanh những lỗi của nhánh `master`.

- `release`: Nơi các tính năng đã hoàn thành và đang trong quá trình test, sau khi test xong thì sẽ được merge vào `master`

- `develop`: Được checkout từ `master`, Nơi Develop clone code để phát triển tính năng

- `feature`: Được checkout từ `develop`. Sau khi hoàn thành một feature sẽ tiến hành merge vào `develop`.

## Khi `push -f`

- Có một option an toàn hơn `--force` là `--force-with-lease`. `--force-with-lease` chỉ cho phép overide những commit của bạn thôi, nếu có những commit của người khác thì nó sẽ `không cho push`
