# Props và State

## 1. Props

- `Props` là một đối tượng lưu giá trị thuộc tính của `thẻ`
- Là cách để truyền dữ liệu từ bên ngoài vào một `Component`
- `Props` chỉ dùng để truyền dữ liệu, không thay đổi được dữ liệu bên trong

## 2. State

- `State` lưu trữ dữ liệu động của `Component` (có thể thay đổi giá trị)
- Cho phép `Component` theo dõi dữ liệu và tự động `re-render` khi có thay đổi
- `State` chỉ có thể sử dụng trong `Component` sinh ra nó

## 3. So sánh

| Tiêu chí                  | Props                                               | State                                                |
|---------------------------|-----------------------------------------------------|------------------------------------------------------|
| Mục đích chính            | Truyền dữ liệu từ ngoài vào                        | Lưu trữ và quản lý dữ liệu nội bộ của Component      |
| Có thể thay đổi không?    | Không – chỉ đọc được                               | Có thể thay đổi thông qua `setState`                 |
| Phạm vi sử dụng           | Từ Component cha → con                             | Nội bộ trong chính Component                         |
| Quyền truy cập            | Thành phần nhận có thể đọc nhưng không chỉnh sửa   | Chỉ Component định nghĩa nó mới có thể sửa đổi       |
| Dẫn đến re-render         | Có – nếu props thay đổi                            | Có – khi `setState` được gọi                         |
