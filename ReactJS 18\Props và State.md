# Props và State

## 1. Props

- `Props` là 1 đối tượng lưu giá trị thuộc tính của `thẻ`
- Cách để truyền dữ liệu từ ngoài vào 1 Component
- `Props` chỉ truyền dữ liệu, không thay đổi dữ liệu

## 2. State

- `State` lưu trữ dữ liệu động của `Component` (<PERSON><PERSON> thể thay đổi giá trị)
- `State` cho phép `Component` theo dõi thông tin để `reRender`
- State chỉ sử dụng được trong `Component` sinh ra nó

## 3. So sánh

- `Props` dùng để truyền dữ liệu, `State` quản lý dữ liệu.
- `Props` chỉ đọc, và không thể bị sửa đổi bởi thành phần nhận nó từ bên ngoài.
- `State` có thể được sửa đổi bởi thành phần của ch<PERSON> nó, nhưng riêng tư (<PERSON><PERSON><PERSON><PERSON> thể bị truy cập từ bên ngoài)
- `Props` chỉ có thể được truyền từ thành phần mẹ đến thành phần con
  Thay đổi `State` nên được thực hiện cùng setState.
