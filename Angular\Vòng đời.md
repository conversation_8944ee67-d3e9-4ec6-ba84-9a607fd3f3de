# Vòng đời

## 1. ngOnChanges()
- G<PERSON><PERSON> trước ngOnInit() và mỗi lần có thay đổi (@Input, @Output thay đổi)
```tsx
  ngOnChanges(changes: SimpleChanges): void {
    console.log(changes);
  }
```

## 2. ngOnInit()
- Gọi `1 lần` và ngay sau ngOnChanges() đ<PERSON>u tiên (Sau khi khởi tạo xong các @Input)
```tsx
  ngOnInit(): void {
    console.log('Component initialized');
  }
```

## 3. ngAfterContentInit()
- <PERSON><PERSON><PERSON><PERSON> gọi `một lần` sau khi Angular đã chèn (inserts) nội dung ngẫu nhiên (ng-content) vào component hoặc directive.
```tsx
  ngAfterContentInit(): void {
    console.log('Content projected into view');
  }
```

## 4. ngAfterViewInit()
- <PERSON><PERSON><PERSON><PERSON> gọi `một lần` sau khi Angular đã khởi tạo view của component và view của các component con.
```tsx
  ngAfterViewInit(): void {
    console.log('View initialized');
  }
```

## 5. ngAfterViewChecked()
- Được gọi sau `mỗi lần` Angular kiểm tra view của component và view của các component con.
```tsx
  ngAfterViewChecked(): void {
    console.log('View checked');
  }
```

## 6. ngOnDestroy()
- Gọi trước khi Component bị hủy
```tsx
  ngOnDestroy(): void {
    console.log('Component destroyed');
  }
```
