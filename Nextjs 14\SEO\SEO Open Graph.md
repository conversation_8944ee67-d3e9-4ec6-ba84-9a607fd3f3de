# SEO Open Graph

`https://nextjs.org/docs/app/api-reference/functions/generate-metadata#opengraph`

- <PERSON>à setting các thẻ `<meta>` tăng thông tin khi chia sẻ Web đến các mạng xã hội (<PERSON><PERSON><PERSON><PERSON> đ<PERSON>, hình <PERSON>nh, mô tả, ...)


## App Router
```tsx
export const metadata = {
  openGraph: {
    title: "Next.js",
    description: "The React Framework for the Web",
    url: "https://nextjs.org",
    siteName: "Next.js",
    images: [
      {
        url: "https://nextjs.org/og.png", // Must be an absolute URL
        width: 800,
        height: 600,
      },
      {
        url: "https://nextjs.org/og-alt.png", // Must be an absolute URL
        width: 1800,
        height: 1600,
        alt: "My custom alt",
      },
    ],
    locale: "en_US",
    type: "website",
  },
};
```

## Page Router

- Thêm thủ công trong <Head>

```tsx
// pages/index.tsx
import Head from "next/head";

export default function Home() {
  return (
    <>
      <Head>
        <title>Next.js</title>
        <meta property="og:title" content="Next.js" />
        <meta property="og:description" content="The React Framework for the Web" />
        <meta property="og:url" content="https://nextjs.org" />
        <meta property="og:site_name" content="Next.js" />
        <meta property="og:image" content="https://nextjs.org/og.png" />
        <meta property="og:image:width" content="800" />
        <meta property="og:image:height" content="600" />
        <meta property="og:locale" content="en_US" />
        <meta property="og:type" content="website" />
      </Head>

      <main>
        <h1>Welcome to Next.js!</h1>
      </main>
    </>
  );
}

```

-----------------------

| `Page Router `             | `App Router `                        |
| -------------------------- | ------------------------------------ |
| Dùng `Head` từ `next/head` | Dùng `metadata` export               |
| Viết tay từng `<meta>`     | Cấu hình qua object `openGraph`      |
| Thêm vào từng page         | Có thể config ở `layout.tsx` cấp cao |
| Linh hoạt nhưng thủ công   | Tự động sinh thẻ đúng chuẩn          |

