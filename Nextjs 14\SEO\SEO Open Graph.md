# SEO Open Graph

`https://nextjs.org/docs/app/api-reference/functions/generate-metadata#opengraph`

- <PERSON><PERSON> setting các thẻ `<meta>` tăng thông tin khi chia sẻ Web đến các mạng xã hội (hình ảnh, mô tả, ...)

```tsx
export const metadata = {
  openGraph: {
    title: "Next.js",
    description: "The React Framework for the Web",
    url: "https://nextjs.org",
    siteName: "Next.js",
    images: [
      {
        url: "https://nextjs.org/og.png", // Must be an absolute URL
        width: 800,
        height: 600,
      },
      {
        url: "https://nextjs.org/og-alt.png", // Must be an absolute URL
        width: 1800,
        height: 1600,
        alt: "My custom alt",
      },
    ],
    locale: "en_US",
    type: "website",
  },
};
```
