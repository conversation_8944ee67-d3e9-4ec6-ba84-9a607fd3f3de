# Facade Pattern

- Cung cấp `giao diện đơn giản` để tương tác với 1 hệ thống phức tạp

```js
// apiFacade.js
const API_BASE_URL = "https://api.example.com";

function getToken() {
  return localStorage.getItem("token");
}

export const apiFacade = {
  get: async (endpoint) => {
    const res = await fetch(`${API_BASE_URL}${endpoint}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getToken()}`,
      },
    });
    return res.json();
  },
};


// Sử dụng đơn giản. Chỉ cần gọi đến getData() là có dữ liệu
const user = await apiFacade.get("/users/123");
```

## Ứng dụng

- `dispatch(action)` của `Redux`. Che giấu sự phức tạp trong `reduce`.
- `Axios Service Layer`:
Tạo 1 `apiService.js` với các hàm như `getUser()`, `createPost()`... bên trong đã cấu hình `baseURL`, `token`, `interceptors` v.v.
- UI component: <Button /> trong các thứ viện UI
