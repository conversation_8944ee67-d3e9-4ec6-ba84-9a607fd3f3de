# Setup VPS mới

`https://edu.duthanhduoc.com/learn/NodeJs-Super?lessonId=357`

## Tạo SSH key và add vào VPS (máy tính cá nhân)

```bash
ssh-keygen -t ed25519 -C "[Email]"
```

- Nhận file `.pub`
- Add thông tin file `.pub` vào SSH key

## Truy cập VPS bằng ssh (máy tính cá nhân)

```bash
ssh root@[ip_address]
```

## Tạo User mới trên linux VPS (trên VPS)

- Tạo 1 User mới, sau này chỉ sử dụng User này, không sử dụng user `root`

```bash
adduser [userName]
```

## Add quyền cho User mới (trên VPS)

- Cấp quyền cho User mới giống như User root

```bash
usermod -aG sudo [userName]
```

## <PERSON><PERSON><PERSON>n qua lại giữa các user (trên VPS)

```bash
su - [userName]
```

## Add SSH key vào user mới (máy t<PERSON>h cá nhân)

```bash
ssh-copy-id -i [đường dẫn public key] [userName]@[địa chỉ VPS]

# Ví dụ
ssh-copy-id -i ~/.ssh/id_ed25519.pub quan@***************
```

## Cài Docker lên VPS Ubuntu (trên VPS)

`https://docs.docker.com/engine/install/ubuntu/`

- Khởi tại

```bash
# Add Docker's official GPG key:
sudo apt-get update
sudo apt-get install ca-certificates curl
sudo install -m 0755 -d /etc/apt/keyrings
sudo curl -fsSL https://download.docker.com/linux/ubuntu/gpg -o /etc/apt/keyrings/docker.asc
sudo chmod a+r /etc/apt/keyrings/docker.asc

# Add the repository to Apt sources:
echo \
  "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.asc] https://download.docker.com/linux/ubuntu \
  $(. /etc/os-release && echo "$VERSION_CODENAME") stable" | \
  sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
sudo apt-get update
```

- Bắt đầu cài Docker

```bash
sudo apt-get install docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin
```

- Test

```bash
sudo docker run hello-world
```

- Nếu có lỗi `permission denied` xử lý như sau: `https://docs.docker.com/engine/install/linux-postinstall/`
