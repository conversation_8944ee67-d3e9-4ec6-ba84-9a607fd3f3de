# Babel

Giú<PERSON> phiên mã `ES6` và `JS<PERSON>` sang `ES5`

## Install các gói sau để làm việc với webpack

- `Babel-core` : phi<PERSON>n mã `ES6` về `ES5`
- `babel-loader` : <PERSON><PERSON><PERSON><PERSON> các files Javascript sử dụng Babel & Webpack
- `babel-present-env` : `Preset` giúp `babel` chuyển đổi mã ES6, ES7 và ES8 sang ES5.
- `babel-present-react` : chuyển `jsx` thành `js`.

```js
// File `.babelrc`
// Cho biết `preset` nào được sử dụng để dịch mã
// env: đây là preset được sử dụng để chuyển mã ES6/ES7/ES8 sang ES5
// react: đây là preset được sử dụng để chuyển mã JSX sang ES5

{
  "presets": ["@babel/preset-env", "@babel/preset-react"]
}
```
