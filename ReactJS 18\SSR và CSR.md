# SSR và CSR

## 1. SSR

Server trả về toàn bộ nội dung (HTML, CSS, JS) được render sẵn về cho Client (Chỉ nội dung trang cần hiển thị nên rất nhanh)
`Ưu điểm:`

- <PERSON><PERSON><PERSON> n<PERSON>ng (Do chỉ trả nội dung của 1 trang)
- SEO (BOT thấy toàn bộ dữ liệu HTML)
- Social sharing

`Nhược điểm:`

- Vì HTML được `server` render (1 trang mỗi lần) nên mỗi khi có sự thay đổi thì cần tải lại trang để `server` trả về HTML mới

## 2. CSR

Server chỉ trả về `trang rỗng index.html` và toàn bộ `file JS` cho Client tự render HTML từ `file JS`

`Ưu điểm:`

- Page chỉ cần load một lần duy nhất. Khi user chuyển trang hoặc thêm dữ liệu, `JavaScript` sẽ lấy và gửi dữ liệu từ `server` qua `AJAX`

`<PERSON>h<PERSON>ợc điểm:`

- Lượng data load về lần đầu khá năng do phải load hết toàn bộ dữ liệu
- Khó SEO

## Tài liệu

`https://viblo.asia/p/server-side-rendering-trong-react-yMnKMYozK7P`
`https://vomanhkien.com/ssr-va-csr/`
`https://toidicodedao.com/2018/09/11/su-khac-biet-giua-server-side-rendering-va-client-side-rendering/`
