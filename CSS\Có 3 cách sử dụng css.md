# Có mấy cách sử dụng CSS trong HTML

- Có 3 cách: `Internal CSS`, `External CSS`, `Inline CSS`

## Internal CSS

- Đặt trong thẻ `<head>` của một trang nhất định css đặt trong thẻ `<style></style>`
- Cần phải được tải về mỗi khi trang được load

```html
<head>
  <style type="text/css">
    p {
      color: white;
      font-size: 10px;
    }
    .center {
      display: block;
      margin: 0 auto;
    }
    #button-go,
    #button-back {
      border: solid 1px black;
    }
  </style>
</head>
```

## External CSS

- Tạo 1 file `.css` và import hoặc sử dụng thẻ `<link>` tời file HTML
- Load trang nhanh hơn
- Lần đầu cần load xong External thì trang mới tải lên hoàn toàn

```html
<head>
  <link rel="stylesheet" type="text/css" href="style.css" />
</head>
```

## Inline CSS

- Sử dụng trực tiếp trong thuộc tính `style` của thẻ HTML
- Dễ dang test nhanh
- Áp dụng từng element nên rất dài dòng, mất thời gian

```html
<p style="color:white;">Something usefull here.</p>
```
