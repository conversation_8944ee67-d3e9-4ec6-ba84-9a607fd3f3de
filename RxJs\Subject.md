# Subject

- `Subject` vừa là một `Observable` vừa là một `Observer`
- <PERSON><PERSON> thể `phát ra` các giá trị cho các `Observers`, và `lắng nghe` các giá trị từ các `Observables`
- Sử dụng để truyền dữ liệu giữa các thành phần không phải cha con, quản lý trạng thái, xử lý sự kiện

## Có 4 loại

### 1. Subject cơ bản

- `Subject`: Phát ra giá trị tới các `Observers` **đã đăng ký từ lúc giá trị được phát ra**.

```ts
import { Subject } from "rxjs";

// Khởi tạo
const subject = new Subject();

// Lắng nghe (Observer)
subject.subscribe({
  next: (v) => console.log(`Observer A: ${v}`),
});

// Ph<PERSON>t sự kiện (Observable)
subject.next(1);
subject.next(2);

// Lắng nghe (Observer)
subject.subscribe({
  next: (v) => console.log(`Observer B: ${v}`),
});

// Phát sự kiện (Observable)
subject.next(3);

// Output
Observer A: 1
Observer A: 2
Observer A: 3
Observer B: 3
```

### 2. BehaviorSubject

- `BehaviorSubject`: **Lưu giữ giá trị hiện tại** và phát giá trị này tới bất kỳ `Observer` **nào mới đăng ký**.

```ts
import { BehaviorSubject } from "rxjs";

const behaviorSubject = new BehaviorSubject(0); // Giá trị khởi tạo là 0

behaviorSubject.subscribe({
  next: (v) => console.log(`Observer A: ${v}`),
});

behaviorSubject.next(1);
behaviorSubject.next(2);

behaviorSubject.subscribe({
  next: (v) => console.log(`Observer B: ${v}`),
});

behaviorSubject.next(3);

// Output
Observer A: 0
Observer A: 1
Observer A: 2
Observer B: 2
Observer A: 3
Observer B: 3
```

### 3. ReplaySubject

- `ReplaySubject`: **Phát lại tất cả các giá trị cũ** (hoặc một số lượng giá trị cũ đã định trước) tới bất kỳ `Observer` **mới đăng ký**.

```ts
import { ReplaySubject } from 'rxjs';

const replaySubject = new ReplaySubject(2); // Phát lại 2 giá trị gần nhất

replaySubject.subscribe({
  next: (v) => console.log(`Observer A: ${v}`)
});

replaySubject.next(1);
replaySubject.next(2);
replaySubject.next(3);

replaySubject.subscribe({
  next: (v) => console.log(`Observer B: ${v}`)
});

replaySubject.next(4);

// Output
Observer A: 1
Observer A: 2
Observer A: 3
Observer B: 2
Observer B: 3
Observer A: 4
Observer B: 4
```

### 4. AsyncSubject

- `AsyncSubject`: **Chỉ phát ra giá trị cuối cùng** (và duy nhất) của luồng khi nó `hoàn thành` tới tất cả các `Observer`.

```ts
import { AsyncSubject } from 'rxjs';

const asyncSubject = new AsyncSubject();

asyncSubject.subscribe({
  next: (v) => console.log(`Observer A: ${v}`)
});

asyncSubject.next(1);
asyncSubject.next(2);
asyncSubject.next(3);

asyncSubject.subscribe({
  next: (v) => console.log(`Observer B: ${v}`)
});

asyncSubject.next(4);
asyncSubject.complete();

// Output
Observer A: 4
Observer B: 4
```
