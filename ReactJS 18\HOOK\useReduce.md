# useReduce()

- Gần giống với Redux: có state, action, và reducer
- Không có Dev tool để xem quá trình thay đổi State

```js
const [state, dispatch] = useReducer(reducer, initial, initFunc?);

// initFunc: là optional, nếu có khi khởi tạo giá trị khởi tạo sẽ là kết quả của hàm initFunc(initial)

function reducer(state, action) {}
```

- Ví dụ:

```js

// Khởi tạo
const reducer = (state, action) => {
    switch (action.type) {
        case 'increase':
            return { ...state, age: state.age + action.payload };
        case 'decrease':
            return { ...state, age: state.age - action.payload };
        default:
            return state;
    }
};

// Sử dụng
const initial = { age: 25 };
const [state, dispatch] = useReducer(reducer, initial);

const handleIncrease = () => {
    dispatch({type: "increase", payload: 5})

    // log giá trị mới ngay sau khi dispatch (<PERSON><PERSON> hàm khởi tạo reduce return giá trị ra nextState)
    const nextState = reducer(state, {type: "increase", payload: 5})
    console.log(nextState.age)
}
```
