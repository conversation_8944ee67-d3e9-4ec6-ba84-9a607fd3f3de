# Suspense for Data Fetching

React 18 mở rộng Suspense không chỉ cho code splitting mà còn cho data fetching.

## 🎯 **Suspense Cơ bản**

### Code Splitting (React 16+)

```jsx
import { Suspense, lazy } from 'react';

const LazyComponent = lazy(() => import('./LazyComponent'));

function App() {
  return (
    <Suspense fallback={<div>Loading component...</div>}>
      <LazyComponent />
    </Suspense>
  );
}
```

### Data Fetching (React 18)

```jsx
import { Suspense } from 'react';

// Giả sử có data fetching library hỗ trợ Suspense
function UserProfile({ userId }) {
  const user = useUser(userId); // Throws promise nếu chưa load xong
  
  return (
    <div>
      <h1>{user.name}</h1>
      <p>{user.email}</p>
    </div>
  );
}

function App() {
  return (
    <Suspense fallback={<div>Loading user...</div>}>
      <UserProfile userId={1} />
    </Suspense>
  );
}
```

## 🔄 **Suspense với useTransition**

```jsx
import { Suspense, useTransition, useState } from 'react';

function App() {
  const [userId, setUserId] = useState(1);
  const [isPending, startTransition] = useTransition();

  const handleUserChange = (newUserId) => {
    startTransition(() => {
      setUserId(newUserId); // Không hiển thị fallback ngay lập tức
    });
  };

  return (
    <div>
      <button onClick={() => handleUserChange(1)}>User 1</button>
      <button onClick={() => handleUserChange(2)}>User 2</button>
      
      {isPending && <div>Switching user...</div>}
      
      <Suspense fallback={<div>Loading user...</div>}>
        <UserProfile userId={userId} />
      </Suspense>
    </div>
  );
}
```

## 🎨 **Nested Suspense**

```jsx
function App() {
  return (
    <Suspense fallback={<div>Loading app...</div>}>
      <Header />
      
      <main>
        <Suspense fallback={<div>Loading sidebar...</div>}>
          <Sidebar />
        </Suspense>
        
        <Suspense fallback={<div>Loading content...</div>}>
          <Content />
        </Suspense>
      </main>
    </Suspense>
  );
}

function Content() {
  return (
    <div>
      <h1>Main Content</h1>
      <Suspense fallback={<div>Loading posts...</div>}>
        <PostsList />
      </Suspense>
    </div>
  );
}
```

## 🚀 **Streaming SSR với Suspense**

### Server Component

```jsx
// app/page.js (Next.js App Router)
import { Suspense } from 'react';

export default function Page() {
  return (
    <div>
      <h1>My App</h1>
      
      {/* Phần này render ngay lập tức */}
      <Header />
      
      {/* Phần này stream sau */}
      <Suspense fallback={<PostsSkeleton />}>
        <Posts />
      </Suspense>
      
      <Suspense fallback={<CommentsSkeleton />}>
        <Comments />
      </Suspense>
    </div>
  );
}

async function Posts() {
  const posts = await fetchPosts(); // Slow API call
  
  return (
    <div>
      {posts.map(post => (
        <div key={post.id}>{post.title}</div>
      ))}
    </div>
  );
}
```

## 🎭 **Error Boundaries với Suspense**

```jsx
import { Suspense } from 'react';
import { ErrorBoundary } from 'react-error-boundary';

function ErrorFallback({ error, resetErrorBoundary }) {
  return (
    <div role="alert">
      <h2>Something went wrong:</h2>
      <pre>{error.message}</pre>
      <button onClick={resetErrorBoundary}>Try again</button>
    </div>
  );
}

function App() {
  return (
    <ErrorBoundary
      FallbackComponent={ErrorFallback}
      onReset={() => window.location.reload()}
    >
      <Suspense fallback={<div>Loading...</div>}>
        <DataComponent />
      </Suspense>
    </ErrorBoundary>
  );
}
```

## 🔧 **Custom Suspense Hook**

```jsx
import { useState, useEffect } from 'react';

// Simple cache
const cache = new Map();

function createResource(promise) {
  let status = 'pending';
  let result;
  
  const suspender = promise.then(
    (data) => {
      status = 'success';
      result = data;
    },
    (error) => {
      status = 'error';
      result = error;
    }
  );

  return {
    read() {
      if (status === 'pending') {
        throw suspender; // Suspense sẽ catch promise này
      } else if (status === 'error') {
        throw result;
      } else if (status === 'success') {
        return result;
      }
    }
  };
}

function useData(url) {
  if (!cache.has(url)) {
    const promise = fetch(url).then(res => res.json());
    cache.set(url, createResource(promise));
  }
  
  return cache.get(url).read();
}

// Sử dụng
function DataComponent() {
  const data = useData('/api/users'); // Có thể throw promise
  
  return (
    <div>
      {data.map(user => (
        <div key={user.id}>{user.name}</div>
      ))}
    </div>
  );
}
```

## 🎨 **Suspense với React Query**

```jsx
import { Suspense } from 'react';
import { useQuery } from '@tanstack/react-query';

function Posts() {
  const { data: posts } = useQuery({
    queryKey: ['posts'],
    queryFn: fetchPosts,
    suspense: true // Enable Suspense mode
  });

  return (
    <div>
      {posts.map(post => (
        <div key={post.id}>{post.title}</div>
      ))}
    </div>
  );
}

function App() {
  return (
    <QueryClient client={queryClient}>
      <Suspense fallback={<div>Loading posts...</div>}>
        <Posts />
      </Suspense>
    </QueryClient>
  );
}
```

## 🎯 **Best Practices**

### 1. Granular Suspense Boundaries

```jsx
// ✅ Tốt: Nhiều boundary nhỏ
function App() {
  return (
    <div>
      <Suspense fallback={<HeaderSkeleton />}>
        <Header />
      </Suspense>
      
      <Suspense fallback={<ContentSkeleton />}>
        <Content />
      </Suspense>
      
      <Suspense fallback={<SidebarSkeleton />}>
        <Sidebar />
      </Suspense>
    </div>
  );
}

// ❌ Không tốt: Một boundary lớn
function App() {
  return (
    <Suspense fallback={<div>Loading everything...</div>}>
      <Header />
      <Content />
      <Sidebar />
    </Suspense>
  );
}
```

### 2. Meaningful Loading States

```jsx
// ✅ Tốt: Loading state có ý nghĩa
<Suspense fallback={<PostsSkeleton />}>
  <Posts />
</Suspense>

// ❌ Không tốt: Generic loading
<Suspense fallback={<div>Loading...</div>}>
  <Posts />
</Suspense>
```

### 3. Combine với useTransition

```jsx
function App() {
  const [tab, setTab] = useState('posts');
  const [isPending, startTransition] = useTransition();

  const switchTab = (newTab) => {
    startTransition(() => {
      setTab(newTab); // Không trigger Suspense fallback ngay lập tức
    });
  };

  return (
    <div>
      <TabButton onClick={() => switchTab('posts')}>
        Posts {isPending && '(Loading...)'}
      </TabButton>
      
      <Suspense fallback={<div>Loading content...</div>}>
        {tab === 'posts' && <Posts />}
        {tab === 'users' && <Users />}
      </Suspense>
    </div>
  );
}
```

## 🚨 **Lưu ý quan trọng**

1. **Suspense chỉ hoạt động với:**
   - React.lazy()
   - Libraries hỗ trợ Suspense (React Query, SWR, Relay)
   - Custom implementations throw promises

2. **Không hoạt động với:**
   - useEffect + useState
   - Async/await trong component body
   - Promise.then() trong render

3. **Server-side rendering:**
   - Cần framework hỗ trợ (Next.js, Remix)
   - Streaming SSR cần setup đặc biệt
