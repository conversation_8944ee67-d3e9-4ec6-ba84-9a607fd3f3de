# Structural & Attribute Directives

## 1. Structural Directives (Chỉ thị cấu trúc)

- **Tác động đến cấu trúc DOM**: thêm, xóa hoặc thay đổi phần tử trong DOM.
- **Dấu hiệu nhận biết**: thường bắt đầu bằng `*`.

### Ví dụ phổ biến

| Tên Directive | Chức năng |
|---------------|-----------|
| `*ngIf`       | Hiển thị phần tử nếu điều kiện đúng |
| `*ngFor`      | Lặp qua danh sách và hiển thị từng phần tử |
| `*ngSwitch`   | Tương tự `switch-case` trong JS |

### Cú pháp

```html
<!-- *ngSwitch -->
<div [ngSwitch]="status">
  <p *ngSwitchCase="'success'">Thà<PERSON> công</p>
  <p *ngSwitchCase="'error'">Thất bại</p>
  <p *ngSwitchDefault>Không xác đ<PERSON>nh</p>
</div>
```

## 2. Attribute Directives (Chỉ thị thuộc tính)

- **Thay đổi giao diện hoặc hành vi của phần tử DOM**, mà không thay đổi cấu trúc DOM.
- **Dấu hiệu nhận biết**: không có dấu `*`, gắn trực tiếp vào thẻ HTML như thuộc tính.

### Ví dụ phổ biến

| Tên Directive | Chức năng |
|---------------|-----------|
| `ngClass`     | Thêm/lấy class theo điều kiện |
| `ngStyle`     | Thêm/lấy style theo điều kiện |
| `[(ngModel)]` | Two-way binding dữ liệu giữa input và component |

## 3. Tự tạo Directive

### Structural Directive (Tùy chỉnh)

```ts
@Directive({
  selector: '[appIfCustom]'
})
export class IfCustomDirective {
  constructor(private templateRef: TemplateRef<any>, private viewContainer: ViewContainerRef) {}

  @Input() set appIfCustom(condition: boolean) {
    if (condition) {
      this.viewContainer.createEmbeddedView(this.templateRef);
    } else {
      this.viewContainer.clear();
    }
  }
}
```

### Attribute Directive (Tùy chỉnh)

```ts
@Directive({
  selector: '[appHighlight]'
})
export class HighlightDirective {
  constructor(private el: ElementRef) {}

  @HostListener('mouseenter') onMouseEnter() {
    this.el.nativeElement.style.backgroundColor = 'yellow';
  }

  @HostListener('mouseleave') onMouseLeave() {
    this.el.nativeElement.style.backgroundColor = null;
  }
}
```

## 4. So sánh

| Đặc điểm             | Structural Directive             | Attribute Directive              |
|----------------------|----------------------------------|----------------------------------|
| Tác động DOM         | Thêm / xóa phần tử               | Thay đổi thuộc tính, giao diện   |
| Dấu hiệu cú pháp     | Có `*` đầu (vd: `*ngIf`)         | Gắn như thuộc tính (vd: `[ngStyle]`) |
| Có thay đổi layout?  | Có                               | Không                            |