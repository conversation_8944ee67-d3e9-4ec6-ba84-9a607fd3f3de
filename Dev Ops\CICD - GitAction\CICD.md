# CICD

`https://viblo.asia/p/cicd-github-actions-va-cac-kien-thuc-co-ban-EoW4oRMrVml`

- Là quá trình tự động hóa `test`, `build` và `deploy` ứng dựng lên các môi trường

## CI

- Quy trình:
- `Tích hợp mã nguồn`: commit và push code lên repository.
- `Kích hoạt CI`: Khi phát ra sự thay đổi, CI server bắt đầu quá trình CI.
- `Build code`: CI server sẽ `build` mã nguồn thành một phần mềm hoàn chỉnh trên môi trường ảo đã setup.
- `Chạy các test case`: Sau khi quá trình `build` hoàn tất, CI bắt đầu chạy kiển tra đơn vị (unit test)
- `Đưa ra thông báo`: Nế<PERSON> có lỗi trong quá trình kiểm tra, CI sẽ đưa ra thông báo lỗi qua email, chat, ...
- `Hợp nhất mã nguồn`: Nếu không có lỗi, CI sẽ tự động hợp nhất mã nguồn.

## CD

- Sau khi CI thành công. Quá trình CD sẽ được kích hoạt để triển khai ứng dụng vào các môi trường của dự án

## Luồng CI/CD deploy lên VPS với Docker

### Luồng CI

- Tạo file `env` (vì trên git k chứa file `env`)
- Từ `Dockerfile` build ra `Image`
- Đăng nhập vào `Docker Hub`
- Push `Image` lên `Docker Hub`

### Luồng CD

- Trỏ vào host `VPS` bằng `SSH`
- Pull `Image` từ `Docker Hub`
- Chạy Docker `Container` từ `Image`
