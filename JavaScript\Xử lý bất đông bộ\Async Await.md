# Async / Await (ES7)

## Async

- <PERSON><PERSON> b<PERSON><PERSON> một hàm bất đồng bộ
- Tự động biến đổi một hàm thông thường thành một Promise

```js
async function handleAsync() {}
```

## Await

- Tạm dừng việc thực hiện các hàm async.
- Await chỉ làm việc với Promises, không hoạt động với callbacks

```js
const result = await someAsyncCall();
```

## Ví dụ

```js
async function getJSONAsync() {
  let data = await axios.get(
    "https://tutorialzine.com/misc/files/example.json"
  );

  return data;
}
```

```text
Khi cần gọi API đồng thời mà các API không rằng buộc lẫn nhau thì nên dùng Promise.all() thay Async/Await
```

## X<PERSON> lý lỗi trong Async / Await

- <PERSON><PERSON> dụng `try / catch`

```js
async function doSomethingAsync() {
  try {
    let result = await someAsyncCall();
  } catch (error) {
    // Bắt tất cả các lỗi xảy ra trong khối try()
  }
}
```
