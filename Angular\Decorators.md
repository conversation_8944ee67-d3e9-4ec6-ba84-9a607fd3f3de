# Decorators

- <PERSON><PERSON> cấp một cách để chú thích và cấu hình các lớp
- <PERSON><PERSON><PERSON> cho Angular biết file `.ts` đó là loại gì

## Các loại phổ biến

### 1. `@NgModule`

- <PERSON><PERSON><PERSON> nghĩa một `Module` (giống 1 nơi chứa các thành phần để chia sẻ và tái sử dụng), bao gồm các `component`, `directive`, `pipe`, và các `service` cần thiết.
- CLI: `ng g module <name>`
- Ứng dụng Angular chứa nhiều Module và được gom lại bởi 1 Module Root (AppModule)
- Cấu trúc 1 ứng dụng gồm: 1 `AppModule` và các `Feature Module` (User/Product/Role/...)
- Các `Feature Module` cần imports[] trong  `AppModule` hoặc imports[] trong Module khác có liên kết với `AppModule`
- Việc import chỉ hoat động 1 chiều (chỉ module nào import thì mới sử dụng được các thành phần của module được import)

```ts
@NgModule({
  declarations: [AppComponent], // Khai báo các components (mới tạo sẽ phải thêm), directive, pipe của Module này
  imports: [BrowserModule, AppRoutingModule], // Khai báp các Module mà Module này sẽ sử dụng (Để có thể sử dụng những component Module đó exports ra)
  providers: [], // Các Module Service
  exports: [], // Các Component, directive, pipe của Module này muốn sử dụng ngoài module này
  bootstrap: [AppComponent], // Component chạy đầu tiên (Thành phần chính của ứng dụng - chỉ có trong AppModule)
})
export class AppModule {}
```

- Ví dụ:
`Cấu trúc`
```css
src/
└── app/
    ├── user/
    │   ├── user.module.ts          // Định nghĩa UserModule
    │   ├── user-routing.module.ts  // Định nghĩa routing cho UserModule
    │   ├── components/
    │   │   ├── user-list/
    │   │   │   ├── user-list.component.ts
    │   │   │   ├── user-list.component.html
    │   │   │   ├── user-list.component.css
    │   │   └── user-detail/
    │   │       ├── user-detail.component.ts
    │   │       ├── user-detail.component.html
    │   │       ├── user-detail.component.css
    │   ├── services/
    │   │   └── user.service.ts      // Định nghĩa các service liên quan tới người dùng
```

`Nội dụng file`
```tsx
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { UserListComponent } from './components/user-list/user-list.component';
import { UserDetailComponent } from './components/user-detail/user-detail.component';
import { UserService } from './services/user.service';
import { UserRoutingModule } from './user-routing.module';

@NgModule({
  declarations: [
    UserListComponent,
    UserDetailComponent
  ],
  imports: [
    CommonModule,
    UserRoutingModule // Định tuyến cho module này
  ],
  providers: [
    UserService
  ]
})
export class UserModule { }
```
- 

### 2. `@Component`

- Định nghĩa 1 `Component`
- CLI: `ng g c <name>`
- Nhớ khai báo `Component` mới tạo vào `declarations` của `Module`

```ts
import { Component } from "@angular/core";

@Component({
  selector: "component-test", // Tên thẻ
  templateUrl: "./component-test.html",
  styleUrl: "./component-test.scss",
})
export class ComponentTestComponent {
  title = "Hoc_Angular";
}
```

- `Standalone Components`: không cần import trong module nào cả và có thể sử dụng khắp mọi nơi
```tsx
import { Component } from '@angular/core';

@Component({
  selector: 'app-greeting',
  standalone: true,
  template: `<h1>Hello, {{name}}!</h1>`,
})
export class GreetingComponent {
  name: string = 'World';
}
```

### 3. `@Injectable`
- Đánh dấu một `class` là có thể được `inject` (tiêm) như một `service`.
- Thường dùng để tạo 1 Service
- Cần thêm `@Injectable()` vào @Pipe/@Directive để có thểm tiêm `service` (Ngoại trừ @Component thì không cần vẫn có thể tiêm)
- Tiêm vào 1 component như sau: constructor(private loggingService: LoggingService) {}

```tsx
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root' | 'any' | '1 Module cụ thể', // Nếu là `root` (Singleton) | `any` (non-singleton) thì không cần đặt vào providers của ngModule
})
export class LoggingService {
  log(message: string) {
    console.log(`Log: ${message}`);
  }
}
```

### 4. `@Pipe`
- Sử dụng để biến đổi dữ liệu trong template
- CLI: `ng generate pipe myCustomPipe`
- Ví dụ:
```tsx
import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'uppercase'
  pure: true // Có gọi lại khi view thay đổi không (Mặc định true chỉ gọi lại khi gía trị thay đổi)
})
export class UppercasePipe implements PipeTransform {
  transform(value: string): string {
    return value.toUpperCase();
  }
}
```
- Sử dung: `<p>{{ 'hello world' | uppercase }}</p>`
  
### 5. `@ViewChild`
- Sử dụng truy cập các thành phần con, directive, hoặc template reference variables từ một thành phần cha
- Nghĩa là đứng tại `component cha` để truy cập vào component con
- Lưu ý: Cần sử dụng trong `ngAfterViewInit `
- Có thể sử dụng `#<tên_tham_chiếu>` vào template muốn truy cập

`Sử dụng khi muốn truy cập vào 1 template đã khai báo trực tiếp từ cha`

```tsx
// Tại phần tử cha
// @ViewChild(<tên_component>) <tên_tham_chiếu>: <khai_báo_kiểu>;
@ViewChild(ChildComponent) child: ChildComponent;
```

### 6. `@ContentChild`
- Sử dụng truy cập các thành phần con, directive, hoặc template reference variables nội dung `được chiếu vào cha (trong <ng-content>)`
- Nghĩa là đứng tại `component cha` để truy cập vào component động được chiếu vào thẻ <ng-content> của template cha
- Lưu ý: cần sử dụng trong `ngAfterContentInit`
- Có thể sử dụng `#<tên_tham_chiếu>` vào template muốn truy cập

`Sử dụng khi muốn truy cập vào 1 template động được chiếu cha, nằm trong <ng-content> của cha`

```tsx
// Component cha
@Component({
  selector: 'app-parent',
  template: `
    <h1>Parent Component</h1>
    <ng-content></ng-content> <!-- Nơi chiếu nội dung -->
  `
})
export class ParentComponent implements AfterContentInit {
// @ContentChild(<tên_tham_chieu_template>) <tên_tham_chiếu>: <khai_báo_kiểu>;
  @ContentChild('ChildComponent') child!: ChildComponent>;

  ngAfterContentInit() {
    console.log(this.child); // Truy cập TemplateRef (là component app-child)
  }
}
----------------------------------
// Component tổng
import { Component } from '@angular/core';

@Component({
  selector: 'app-root',
  template: `
    <app-parent>
      <app-child></app-child> <!-- ChildComponent được chiếu vào ParentComponent -->
    </app-parent>
  `
})
export class AppComponent {}
```
### 7. `@Directive`
- Tạo ra các chỉ thị để tương tác với DOM
- Ví dụ `<div appHighlight>...</div>`: appHighlight là chỉ thị đã được tạo và sử dụng lên thẻ div

```tsx
import { Directive, ElementRef, Renderer2, HostListener } from '@angular/core';

@Directive({
  selector: '[appHighlight]' // Bạn có thể sử dụng chỉ thị này với thuộc tính "appHighlight"
})
export class HighlightDirective {
  constructor(private el: ElementRef, private renderer: Renderer2) {}

  @HostListener('mouseenter') onMouseEnter() {
    this.highlight('yellow'); // Khi chuột vào, làm nổi bật màu vàng
  }

  @HostListener('mouseleave') onMouseLeave() {
    this.highlight(null); // Khi chuột rời, khôi phục màu
  }

  private highlight(color: string) {
    this.renderer.setStyle(this.el.nativeElement, 'backgroundColor', color);
  }
}
```

### 7. `@HostListener`
- Dùng để lắng nghe các sự kiện DOM

```tsx
`@HostListener(eventName: string, args?: string[])`
- eventName: Tên sự kiện muốn lắng nghe (ví dụ: 'click', 'mouseenter', 'mouseleave', v.v.).
- args: Một mảng chứa các đối số có thể được truyền vào hàm xử lý sự kiện.
```

- `@Input`
- `@Output`
