# Observer Pattern

Là mối quan hệ một-nhiều giữa các đối tượng:
- Khi 1 đối tượ<PERSON> (Subject) thay đổi dữ liệu
- Thì nhiều đối tượng phụ (Observer) sẽ được thông báo và cập nhật

```js
// Tạo EventBus đơn giản
const EventBus = {
  observers: [],    // <PERSON>h sách các "người đang lắng nghe" (subscriber)

  // Hàm đăng ký lắng nghe
  subscribe(callback) {
    this.observers.push(callback);
  },
  
  // Hàm thông báo cho tất cả observer khi có dữ liệu mới
  publish(data) {
    this.observers.forEach((cb) => cb(data));
  }
};

// Component A đăng ký nhận dữ liệu
EventBus.subscribe((msg) => {
  console.log("A nhận được:", msg);
});

// <PERSON>hi gọi sẽ gửi tới tất cả những người đăng ký trong mảng `observers`
EventBus.publish("Xin chào từ B!");
```

## Ứng dụng

- Context, Redux: Khi `state` thay đổi, các component sẽ lắng nghe và cập nhập dữ liệu mới (re-render)
