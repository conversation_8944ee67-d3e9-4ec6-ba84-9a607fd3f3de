# API Socket Client

## I. IO

- Option : `https://socket.io/docs/v4/client-options/`

```ts
import { io } from "socket.io-client";

const socket = io("ws://example.com/my-namespace", {
  reconnectionDelayMax: 10000,
  auth: {
    token: "123",
  },
  query: {
    "my-key": "my-value",
  },
});
```

## II. Socket

- Socket là lớp cơ bản tương tác với máy chủ
- Socket thuộc về 1 NameSpace nhất định (mặc định '/')

### Hàm tương tác Socket (Phương pháp - Methods)

- `socket.on([eventName], [callback])` : Lắng nghe 1 sự kiện từ tất cả những người dùng đang connect

```ts
socket.on("news", (data, callback) => {
  console.log(data);

  callback({
    status: "Đ<PERSON> nhận",
  });
});
```

- `socket.onAny([callback])` : <PERSON><PERSON><PERSON> nghe tất cả sự kiện (`Không có callback(): tr<PERSON> lời khi nhận`)

```ts
socket.onAny((event, data) => {
  console.log(`got ${event}`);
});
```

- `socket.once([eventName], [callback])` : Chỉ lắng nghe sự kiện 1 lần và hủy sau đó

```ts
socket.once("my-event", (data, callback) => {
  console.log(data);

  callback({
    status: "Đã nhận",
  });
});
```

- `socket.emit([eventName], [...args], [ack])` : Gửi sự kiện tới tất cả những người dùng đang connect

```ts
socket.emit("request", { text: "Hello" }, (err, response) => {
  if (err) {
    // Có thể gọi lại socket.emit để thử gửi lại
  } else {
    // Có thể bắn thông báo đã gửi thành công
    console.log(response); // "Đã nhận"
  }
});
```

- `socket.emitWithAck([eventName], [...args])` : Trả về kết quả khi người nhận đã nhận sự kiện

```ts
const response = await socket.emitWithAck("hello", "world");
```

- `socket.listeners([eventName])` : Trả về danh sách người nghe sự kiện eventName

```ts
console.log(socket.listeners("my-event"));
```

- `socket.off([eventName])` : Kết thúc lắng nghe 1 sự kiện

```ts
// remove all listeners for that event
socket.off("my-event");

// remove all listeners for all events
socket.off();
```

- `socket.timeout()` : Chờ 1 khoảng thời gian trước khi trả về lỗi

```ts
socket.timeout(5000).emit("my-event", (err) => {
  if (err) {
    // Có thể gọi lại socket.emit() để gửi lại
  }
});
```

- `socket.send()` : Giống `socket.emit()`, sự kiện mặc định `message`
- `socket.broadcast.emit()`: Gửi tới tất cả những người dùng đang connect trừ người gửi
- `socket.disconnect()` : Ngắt kết nối Socket thủ công (Không thử kết nối lại)
- `socket.connect()` : Kết nối Socket thủ công

### Thuộc tính Socket (Attributer)

- `socket.active` - Socket có tự động thử kết nối lại hay không
- `socket.connected` - Socket có đang kết nối không
- `socket.disconnected` - Socket có bị ngắt kết nối không
- `socket.recovered` - Trạng thái kết nối có được khôi phục thành công trong lần kết nối lại gần đây nhất hay không
- `socket.id` - Mã định danh cho 1 phiên socket (Đạt lại mỗi khi connect, 2 tab sẽ có socket.id khác nhau)

```ts
socket.on("connect_error", (error) => {
  if (socket.active) {
    // Socket có thử kết nối lại
  } else {
    // Nếu không, xử lý kết nối lại thủ công với `socket.connect()` tại đây
  }
});
```

### Sự kiện Socket (Event)

`ping` - Khi nhận được gói ping từ máy chủ
`connect` − Khi client kết nối thành công.
`connect_error` − Khi client kết nối thất bại.
`connecting` − Khi client đang thực hiện kết nối.
`disconnect` − Khi client mất kết nối hoặc ngắt kết nôi.
`connect_failed` − Khi client kết nối server fail.
`error` − Khi có lỗi event được gửi từ server.
`message` − Khi server gửi message sử dụng hàm send.
`reconnect` − Khi kết nối lại tới server thành công.
`reconnect_error` − Khi có lỗi khi kết lối lại
`reconnecting` − Khi client đang trong quá trình kết nối.
`reconnect_failed` − Khi kết nối fail.

```ts
// Sử dụng
socket.on("connect_failed", () => {});
```

## Room trong Socket

- Là kênh cho phép những người đang connect có thể tham gia hoặc rời đi
- Dữ liệu chỉ được gửi trong phạm vị room

```js
io.on("connection", (socket) => {
  // Tham gia room có tên 'room1'
  socket.join("room1");

  // Gửi sự kiện đến tất cả người connect trong `room1`
  io.to("room1").emit("hello", { text: "Hello" });

  // Gửi sự kiện đến tất cả người connect ngoài trừ `room1`
  io.except("room1").emit("hello", "world");

  // Rời khỏi `room1`
  socket.leave("room1");
});
```
