# Modern Angular Features (14+)

## 1. Control Flow Syntax (Angular 17+)

### @if - Thay thế *ngIf
```html
<!-- Cũ -->
<div *ngIf="user; else noUser">
  Welcome {{ user.name }}!
</div>
<ng-template #noUser>
  <div>Please login</div>
</ng-template>

<!-- Mới -->
@if (user) {
  <div>Welcome {{ user.name }}!</div>
} @else {
  <div>Please login</div>
}
```

### @for - Thay thế *ngFor
```html
<!-- Cũ -->
<div *ngFor="let item of items; trackBy: trackByFn; let i = index">
  {{ i }}: {{ item.name }}
</div>

<!-- Mới -->
@for (item of items; track item.id) {
  <div>{{ $index }}: {{ item.name }}</div>
} @empty {
  <div>No items found</div>
}
```

### @switch - Thay thế *ngSwitch
```html
<!-- Cũ -->
<div [ngSwitch]="status">
  <div *ngSwitchCase="'loading'">Loading...</div>
  <div *ngSwitchCase="'success'">Success!</div>
  <div *ngSwitchDefault>Unknown status</div>
</div>

<!-- Mới -->
@switch (status) {
  @case ('loading') {
    <div>Loading...</div>
  }
  @case ('success') {
    <div>Success!</div>
  }
  @default {
    <div>Unknown status</div>
  }
}
```

---

## 2. Signals (Angular 16+)

### Basic Signals
```ts
import { signal, computed, effect } from '@angular/core';

@Component({...})
export class CounterComponent {
  // Writable signal
  count = signal(0);
  
  // Computed signal
  doubleCount = computed(() => this.count() * 2);
  
  // Effect
  constructor() {
    effect(() => {
      console.log('Count changed:', this.count());
    });
  }
  
  increment() {
    this.count.update(value => value + 1);
  }
  
  reset() {
    this.count.set(0);
  }
}
```

### Signal trong Template
```html
<div>
  <p>Count: {{ count() }}</p>
  <p>Double: {{ doubleCount() }}</p>
  <button (click)="increment()">+</button>
  <button (click)="reset()">Reset</button>
</div>
```

### Signal với Arrays/Objects
```ts
interface User {
  id: number;
  name: string;
}

@Component({...})
export class UserListComponent {
  users = signal<User[]>([]);
  
  addUser(user: User) {
    this.users.update(users => [...users, user]);
  }
  
  removeUser(id: number) {
    this.users.update(users => users.filter(u => u.id !== id));
  }
  
  updateUser(id: number, updates: Partial<User>) {
    this.users.update(users => 
      users.map(u => u.id === id ? { ...u, ...updates } : u)
    );
  }
}
```

---

## 3. Standalone APIs

### bootstrapApplication
```ts
// main.ts
import { bootstrapApplication } from '@angular/platform-browser';
import { AppComponent } from './app/app.component';
import { provideRouter } from '@angular/router';
import { provideHttpClient } from '@angular/common/http';

bootstrapApplication(AppComponent, {
  providers: [
    provideRouter(routes),
    provideHttpClient(),
    // Other providers...
  ]
});
```

### Standalone Component với Providers
```ts
@Component({
  standalone: true,
  selector: 'app-user',
  imports: [CommonModule, FormsModule],
  providers: [UserService], // Component-level providers
  template: `...`
})
export class UserComponent {
  constructor(private userService: UserService) {}
}
```

## 4. Optional Injectors

### inject() Function
```ts
import { inject } from '@angular/core';

@Component({...})
export class MyComponent {
  // Thay vì constructor injection
  private userService = inject(UserService);
  private router = inject(Router);
  
  // Optional injection
  private optionalService = inject(OptionalService, { optional: true });
  
  // Self injection
  private selfService = inject(SelfService, { self: true });
}
```

### Functional Interceptors
```ts
import { HttpInterceptorFn } from '@angular/common/http';

export const authInterceptor: HttpInterceptorFn = (req, next) => {
  const authService = inject(AuthService);
  const token = authService.getToken();
  
  if (token) {
    req = req.clone({
      setHeaders: { Authorization: `Bearer ${token}` }
    });
  }
  
  return next(req);
};

// Provide interceptor
bootstrapApplication(AppComponent, {
  providers: [
    provideHttpClient(
      withInterceptors([authInterceptor])
    )
  ]
});
```

---

## 5. View Encapsulation

### Encapsulation Modes
```ts
import { ViewEncapsulation } from '@angular/core';

@Component({
  selector: 'app-example',
  template: `<div class="container">Content</div>`,
  styles: [`
    .container { 
      background: blue; 
    }
  `],
  encapsulation: ViewEncapsulation.Emulated // Default
  // encapsulation: ViewEncapsulation.None     // Global styles
  // encapsulation: ViewEncapsulation.ShadowDom // Shadow DOM
})
export class ExampleComponent {}
```

---

## 6. Dynamic Components

### ComponentRef
```ts
import { ViewContainerRef, ComponentRef } from '@angular/core';

@Component({
  template: `
    <div #container></div>
    <button (click)="loadComponent()">Load</button>
  `
})
export class DynamicComponent {
  @ViewChild('container', { read: ViewContainerRef }) 
  container!: ViewContainerRef;
  
  loadComponent() {
    this.container.clear();
    
    const componentRef: ComponentRef<UserComponent> = 
      this.container.createComponent(UserComponent);
    
    // Pass data
    componentRef.instance.user = { name: 'John' };
    
    // Listen to outputs
    componentRef.instance.userSelected.subscribe(user => {
      console.log('User selected:', user);
    });
  }
}
```

---

## 7. Content Projection Advanced

### Multi-slot Projection
```ts
@Component({
  selector: 'app-card',
  template: `
    <div class="card">
      <header>
        <ng-content select="[slot=header]"></ng-content>
      </header>
      <main>
        <ng-content></ng-content>
      </main>
      <footer>
        <ng-content select="[slot=footer]"></ng-content>
      </footer>
    </div>
  `
})
export class CardComponent {}
```

```html
<!-- Usage -->
<app-card>
  <h1 slot="header">Card Title</h1>
  <p>Card content goes here</p>
  <button slot="footer">Action</button>
</app-card>
```

### Conditional Content Projection
```ts
@Component({
  selector: 'app-conditional',
  template: `
    <div *ngIf="showContent">
      <ng-content></ng-content>
    </div>
    <div *ngIf="!showContent">
      <ng-content select="[slot=fallback]"></ng-content>
    </div>
  `
})
export class ConditionalComponent {
  @Input() showContent = true;
}
```

---

## 8. Performance Optimizations

### OnPush Strategy với Signals
```ts
@Component({
  changeDetection: ChangeDetectionStrategy.OnPush,
  template: `
    <div>{{ count() }}</div>
    <div>{{ expensiveComputation() }}</div>
  `
})
export class OptimizedComponent {
  count = signal(0);
  
  // Computed signals tự động optimize
  expensiveComputation = computed(() => {
    console.log('Computing...');
    return this.count() * 1000;
  });
}
```

### TrackBy Functions
```ts
@Component({
  template: `
    @for (item of items; track trackByFn($index, item)) {
      <div>{{ item.name }}</div>
    }
  `
})
export class ListComponent {
  items = signal<Item[]>([]);
  
  trackByFn(index: number, item: Item): number {
    return item.id; // Unique identifier
  }
}
```

---

## 9. Tổng kết Modern Features

| Feature | Angular Version | Benefit |
|---------|----------------|---------|
| Control Flow | 17+ | Cleaner templates, better performance |
| Signals | 16+ | Fine-grained reactivity, better performance |
| Standalone | 14+ | Simpler architecture, better tree-shaking |
| inject() | 14+ | Functional injection, more flexible |

**Migration Strategy:**
- ✅ Start với Standalone components cho features mới
- ✅ Migrate từ từ từ NgModules sang Standalone
- ✅ Sử dụng Signals cho reactive state
- ✅ Adopt Control Flow syntax khi upgrade lên v17+
