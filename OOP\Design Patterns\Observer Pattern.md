# Observer Pattern

- <PERSON><PERSON> cho phép một `đối tượng` (g<PERSON><PERSON> là `subject` hoặc `publisher`) theo dõi và thông báo cho các `đối tượng phụ thuộc` của nó (gọi là `observers` hoặc `subscribers`) khi `trạng thái` của nó `thay đổi`

```js
// Định nghĩa Subject
class Subject {
  constructor() {
    this.observers = [];
  }

  // Thêm đối tượng nghe
  addObserver(observer) {
    this.observers.push(observer);
  }

  // Bắn thông báo đến các đối tượng nghe
  notifyObservers(data) {
    this.observers.forEach((observer) => observer.update(data));
  }
}

// Định nghĩa Observer
class Observer {
  update(data) {
    console.log("Dữ liệu mới:", data);
  }
}

// Sử dụng
const subject = new Subject();
const observer1 = new Observer();
const observer2 = new Observer();

subject.addObserver(observer1);
subject.addObserver(observer2);

// Bắn dữ liệu đến các `Observer` trong `subject` gọi đến update() của các `Observer`
subject.notifyObservers("Hello, world!");
```

## Ứng dụng

- Khi `giá xăng` thay đổi. Thì `giá sản phẩm` sẽ thay đổi tỉ lể theo giá xăng được cập nhập
- Khi dữ liệu của `subject` được cập nhập mới. Tất cả các `Observer` sẽ bắt được sự thay đổi này
