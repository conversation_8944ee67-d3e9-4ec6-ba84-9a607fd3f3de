# Getter Setter

- Sử dụng khi muốn `truy cập` và `thay đổi` gi<PERSON> trị 1 thuộc tính `private` từ bên ngoài `class`
- `private` là thuộc tính chỉ có thể được truy cập và thay đổi từ bên trong `class`
- Không cho phép thay đổi thuộc tính trực tiếp từ bên ngoài mà phải thông qua các phương thức `Getter Setter`
- Class sử dụng `Getter Setter` gọi là `Class đóng gói`

```js
class SessionToken {
  // Có thể có nhiều thuộc tính `private` khác
  private token = "";

  // `value` chỉ là tên để gọi (tự đặt theo mong muốn)
  get value() {
    return this.token;
  }

  // `value` chỉ là tên để gọi (tự đặt theo mong muốn)
  set value(token: string) {
    if (!token) {
      throw new Error("Email cannot be empty");
    }
    this.token = token;
  }
}

export const ClientSessionToken = new SessionToken();

// Sử dụng
// Get
console.log(ClientSessionToken.value;)

// Set
ClientSessionToken.value = "1123";
```
