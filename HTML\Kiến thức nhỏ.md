# Kiến thức nhỏ

## 1. `display: inline`, `block` và `inline-block`

### `display: inline`

- Các item sẽ nằm trên cùng một dòng, ví dụ như `<span>`
- <PERSON><PERSON><PERSON><PERSON> thể set width và height
- Với `margin` và `padding`: chỉ điều chỉnh được `left and right`
- Tự xuông dòng nếu vượt quá độ dài của dòng

### `display: block`

- Item luôn xuống dòng mới và chiếm toàn bộ dòng nếu không set `width` (`<div>`)

### `display: inline-block`

- Các item sẽ nằm trên cùng một dòng, nhưng có thẻ set `width, height, margin, padding` đủ 4 hướng
- Thường tạo `navbar`.

## 2. Thẻ `<a>`

`https://vietnix.vn/the-a-trong-html/#:~:text=HTML%20l%C3%A0%20g%C3%AC%3F-,Th%E1%BA%BB%20a%20href%20trong%20HTML%20l%C3%A0%20g%C3%AC%3F,%C4%91%C3%B3%20m%C3%A0%20b%E1%BA%A1n%20mong%20mu%E1%BB%91n.`

```html
<a href=”https://vietnix.vn/” target=”_blank” title=”Vietnix” rel=”follow, index”>vietnix.vn</a>
```

- Chứa liên kết điều hướng
- `href`: Nơi chứa link,
- Nếu `href` là `#` thì sẽ không điều hướng đi đâu cả
- Nếu `href` là `id` của 1 thẻ khác trong trang, sẽ nhẩy đến vị trí thẻ đó

## 3. Thẻ `<ul>`, `<ol>` và `<li>`

- `<ul>`: Tạo dánh sách không có thứ tự số
- `<ol>`: Tạo dánh sách có thứ tự số
- `<li>`: Thẻ Item

```html
<ul>
  <li>Táo</li>
  <li>Mận</li>
  <li>Đào</li>
</ul>
```

## 4. Table

`https://webcoban.vn/html/cach-tao-bang-table-trong-html.html`

- `<table>`: Xác định một cái bảng.
- `<tr>`: Xác định một hàng bên trong bảng.
- `<th>`: Xác định một ô (tiêu đề) bên trong hàng.
- `<td>`: Xác định một ô (bình thường) bên trong hàng.

```html
<table border="1">
  <tr>
    <th>Họ tên</th>
    <th>Ngày sinh</th>
    <th>Giới tính</th>
  </tr>
  <tr>
    <td>Trần Anh Đức</td>
    <td>03/08/1993</td>
    <td>Nam</td>
  </tr>
  <tr>
    <td>Kiều Thị Thu Hằng</td>
    <td>04/09/1991</td>
    <td>Nữ</td>
  </tr>
  <tr>
    <td>Vương Thị Lê Na</td>
    <td>06/10/1991</td>
    <td>Nữ</td>
  </tr>
</table>
```

- Gộp ô theo chiều ngang:
  `colspan="số ô muốn gộp lại với nhau"`
- Gộp ô theo chiều dọc:
  `rowspan="số ô muốn gộp lại với nhau"`
