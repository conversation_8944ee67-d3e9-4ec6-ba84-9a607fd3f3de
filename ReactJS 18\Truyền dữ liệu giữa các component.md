# Truyền dữ liệu giữa các componenet

## Chuyền dữ liệu từ `cha` xuống `con`

- Thông qua `Props`

```jsx
import React, { useState } from "react";

// Tại Component Cha
function Parent() {
  return (
    <div>
      <Child dataFromParent="Hello" /> // Truyền dữ liệu qua Props
    </div>
  );
}

// Tại Component Con
function Child(props) {
  return (
    <>
      <div>{props.dataFromParent}</div>
    </>
  );
}
```

## Chuyền dữ liệu từ `con` lên `cha`

- Truyền hàm `Callbacks` thông qua `Props`

```jsx
import React, { useState } from "react";

// Tại Component Cha
import React, {useState} from 'react';

function Parent() {
  const [message, setMessage] = useState('')

  callbackFunction = (childData) => {
    setMessage(childData)
  },

  return (
    <div>
      <Child parentCallback={callbackFunction}/>
      <p> {message} </p>
    </div>
  );
}

// Tại Component Con
function Child(props) {
  sendData = () => {
    props.parentCallback("Message from Child");
  },

  return ()
};
```

## Tại `con` gọi hàm của `cha`

- Truyền hàm `Callbacks` thông qua `Props`

```jsx
import React, { useState } from "react";

// Tại Component Cha
import React, {useState} from 'react';

function Parent() {
  const [message, setMessage] = useState('')

  callbackFunction = () => {
    console.log('run')
  },

  return (
    <div>
      <Child parentCallback={callbackFunction}/>
      <p> {message} </p>
    </div>
  );
}

// Tại Component Con
function Child(props) {
  sendData = () => {
    props.parentCallback();
  },

  return ()
};
```

## Tại `cha` gọi hàm của `con`

- Sử dụng `ref`, `forwardRef`, `useImperativeHandle`
- `useImperativeHandle` giúp biết được những hàm nào của Component `con` được `expose`

```js
// Tại component Cha
function Parent() {
    const childRef = useRef(null)
    handleCallChild = () => {
        childRef.current.getLog() // Gọi hàm thông qua ref
    },

    return (
        <div>
            <Child ref={childRef}/>
        </div>
    );
}

// Tại component Con
const Child = React.forwardRef((props, ref) => {

    useImperativeHandle(ref, () => ({
        getLog: () => {
            console.log('run')
        },
    }));

    return (
        <div>
            <p>{number}</p>
        </div>
    );
});
```
