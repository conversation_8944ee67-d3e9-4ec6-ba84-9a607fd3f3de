# Desport

## Web Desport

- <PERSON><PERSON><PERSON> dựng màn <PERSON>u<PERSON><PERSON> lý <PERSON> (<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>i đấu tham gia, <PERSON>h<PERSON><PERSON> tin cá nhân, <PERSON><PERSON><PERSON><PERSON> báo)
- <PERSON><PERSON>y dựng mà<PERSON> (CRUD Nhóm)
- <PERSON><PERSON><PERSON> dựng Component Cart Giải đấu
- <PERSON><PERSON><PERSON> dựng UI Đăng ký giải đấu
- <PERSON><PERSON>y dựng UI Thông tin giải đấu (Live, Chat, Nhóm tham gia)
- <PERSON><PERSON>y dựng UI phân chia nhóm trong giải đấu (Kéo thả)
- Xây dựng Noti
- Oauth2.0 với GG, Discord
- Tạo AirDrop, mini game
- Reponsive Web (Android, IOS, MAC)
- Check quyền
- Tạo Slide
- iframe <PERSON>
- SEO, robots (tool Facebook)
- <PERSON><PERSON> ngôn ngữ i18n
- Redux
- Context

## Xâ<PERSON> dựng web Admin

- <PERSON><PERSON><PERSON><PERSON> lý User, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> k<PERSON>, <PERSON><PERSON><PERSON><PERSON> đấ<PERSON>, <PERSON>er
- Form React-Hook-Form

## <PERSON><PERSON><PERSON> dựng web Insights

- <PERSON> c<PERSON><PERSON>, <PERSON><PERSON><PERSON> đầ<PERSON> tư, <PERSON><PERSON><PERSON><PERSON> n<PERSON><PERSON> b<PERSON>, tin t<PERSON>c
- Slide
- iframe Youtobe
