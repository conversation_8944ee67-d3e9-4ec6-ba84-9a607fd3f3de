# Bắt sự kiện gửi qua các Tab

## BroadcastChannel

- Tạo 1 kênh truyền thông để gửi và nhận dữ liệu giữa các Tab

```js
// Tạo một BroadcastChannel với tên 'example_channel'
const bc = new BroadcastChannel("example_channel");

// Gửi một thông điệp khi nút được nhấn
const handleClick = () => {
  const message = "Hello from tab " + Math.random();
  bc.postMessage(message);
};

// Nhận thông điệp và hiển thị nó trong div
useEffect(() => {
  bc.onmessage = (event) => {
    console.log(event);
  };

  // Cleanup khi component bị unmount
  return () => {
    bc.close();
  };
}, [bc]);
```
