# Responsive

## <PERSON><PERSON><PERSON> thiế<PERSON> kế `Mobile-first` trước

- Tiếp cận người dùng di động (Do tối ưu hóa hình ảnh và nội dung)
- <PERSON><PERSON><PERSON><PERSON> viết lại CSS (<PERSON><PERSON> thể sự dụng CSS Mobile để tái sử dụng cho các kích thước khác)
- T<PERSON><PERSON> hơn cho tốc độ tải trang
- Ph<PERSON>t triển từ cơ sở đơn giản

## Sử dụng `@media`

```css
/*Ipad ngang(1024 x 768)*/
@media screen and (max-width: 1024px) {
}
/*Ipad dọc(768 x 1024)*/
@media screen and (max-width: 768px) {
}
/*Tablet nhỏ(480 x 640)*/
@media screen and (max-width: 480px) {
}
/*Iphone(480 x 640)*/
@media screen and (max-width: 320px) {
}
/*Smart phone nhỏ*/
@media screen and (max-width: 240px) {
}
```
