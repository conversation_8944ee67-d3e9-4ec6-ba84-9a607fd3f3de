# AWS

AWS (Amazon web services) là một nền tảng điện toán đám mây cung cấp:

- <PERSON><PERSON><PERSON> trữ
- T<PERSON>h toán
- <PERSON><PERSON> sở dữ liệu
- Phân tich

## Amazon S3 (<PERSON>ị<PERSON> vụ lưu trữ: file, video, ảnh, ...)

- Amazon S3 là không gian để `lưu trữ` và `trích xuất` bất cứ dữ liệu nào ở bất cứ đâu
- Lưu trữ dưới dạng các `đối tượng` bên trong `bucket`

### So sánh S3 với DB truyền thống

`Amazon S3`

- <PERSON><PERSON><PERSON> năng lưu trữ không giới hạn
- Chi phí thấp
- `Truy cập trực tiếp` trên dữ liệu mà không cần tải toàn bộ đối tượng

```text
Thường dùng lưu trữ tệp lớn, kh<PERSON><PERSON> có cấu trúc
```

`DB tru<PERSON>ền thống`

- <PERSON><PERSON> giới hạn về dung lượng, kh<PERSON> năng mở rộng
- <PERSON> phí cao hơn
- Tối ưu cho lưu trữ có cấu trúc và truy vấn phức tạp

```text
Thường dùng lưu hệ thống bảng, có cấu trúc
```

## Amazon SES (Gửi Mail số lượng lớn)

## Amazon EC2 (Dịch vụ VPS)
