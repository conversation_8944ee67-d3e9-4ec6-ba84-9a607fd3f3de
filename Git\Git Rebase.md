# Git rebase (Hợp nhất code)

- <PERSON><PERSON>n sử dụng `rebase` để tạo base mới cho nhánh `feature` bằng nhánh `master`
- Không `rebase` nhưng nhánh khác vào nh<PERSON>h tổng (tại `master` - `git rebase feature`)

```js
// Tại nhánh `feature`
git rebase master
// Hoặc
git pull --rebase master
```

## Nguyên lý

- Thay đổi base `nh<PERSON>h hiện tại` bằng `nhánh đích` sau đó đem hết commit của `nhánh hiện tại` lên đầu

  _**Minh họa**_
  ![alt](https://nhobethoi.com/wp-content/uploads/2021/06/git-merge-gop-cac-nhanh-thanh-nhanh-duy-nhat-1.png)

- Tại nhánh Feature: `git rebase master`
  ![alt](https://nhobethoi.com/wp-content/uploads/2021/06/git-rebase-gop-nhanh.png)

## Ưu nhược

- Không biết được thời điểm `merge`
- Tạo ra cấu trúc `commit` liền mạch
- `Merge` từng commit 1. Khi có conflict thì dừng lại để xử lý xong mới merge tiếp => `Xử lý mất thời gian nhưng dễ dàng hơn`
- Thay đổi lịch sử commit
- Khi push phải dùng lệnh `git push -f` (`Nguy hiểm - overide code`)

## Xử lý rebase bị conflic

- Fix conflict
- `git add .`
- `git rebase --continue`
- `git push --force-with-lease`
  Hủy bỏ quá trình rebase `git rebase --about`

**Gộp commit và sửa commit với `git rebase`**

### 1. Gộp commit

```js
// Gộp 3 commit đầu tiên
git rebase -i HEAD~3
```

- Xuất hiện màn hình edit trạng thái commit (gộp commit phía sau vào commit phía trước)
- Sửa các commit muốn gộp từ `pick` thành `s` (Cần có 1 commit `pick` là commit chính để gộp vào)
- Xuất hiện màn hình để edit tên commit (Sửa tên cho commit gộp)
- Lưu lại và `push --force-with-lease` lên

### 2. Sửa message một commit đã được push

Tương tự đổi commit muốn sửa tên đổi commit đó từ `pick` sang `r`

```js
p: pick - giữ lại commit
r: reword - giữ lại commit và sửa message
s: squash - bỏ qua commit nhưng tích hợp log vào commit liền trước
f: fixup - bỏ qua commit và xoá hoàn toàn log commit
```
