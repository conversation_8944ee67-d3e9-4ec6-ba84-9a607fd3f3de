# Pages Router

## 📁 1. <PERSON><PERSON><PERSON><PERSON>

- Pages Router sử dụng thư mục `/pages`.
- Mỗi file `.js` hoặc `.tsx` tương ứng với một route.

```bash
/pages
  ├── index.js          // route: "/"
  ├── about.js          // route: "/about"
  ├── blog
  │   └── [id].js     // route: "/blog/:id"
  └── api
      └── hello.js      // API route: "/api/hello"
```

## 🚦 2. useRouter()

- <PERSON>hi sử dụng `useRouter()`, cần kiểm tra `useRouter()` đã sẵn sang chưa

```tsx
import { useRouter } from "next/router";

const router = useRouter();

useEffect(() => {
  if (router.isReady) {
    console.log(router.query);
  }
}, [router.isReady]);
```

### Get tham số từ URL

```jsx
import { useRouter } from "next/router";

function BlogDetail() {
  const router = useRouter();

  const { search, page } = router.query; // URL: /products?search=book&page=2
  const { id } = router.query; // pages/post/[id]
}
```

### Điều hướng

- Sử dụng `router.push()` hoặc `<Link>`

```jsx
// pages/blog/[id].js
import { useRouter } from "next/router";

function Home() {
  const router = useRouter();

  const goToPost = () => {
    router.push("/post/123");
  };
}
```

| API                 | Ý nghĩa                      |
| ------------------- | ---------------------------- |
| `router.push()`     | Điều hướng đến trang mới     |
| `router.replace()`  | Điều hướng không lưu lịch sử |
| `router.back()`     | Quay lại trang trước         |
| `router.reload()`   | Tải lại trang                |
| `router.prefetch()` | Tải sẵn trang cho Link       |

## 🚦 3. Data Fetching

- Phục vụ lấy data để server render HTML, k phục vụ UI

### getStaticProps() - SSG

- Lấy dữ liệu tại thời điểm build (tự động chạy hàm khi build)
- Trả data về props, component nhận props đó để sinh trước HTML
- Sử dụng khi dữ liệu không thay đổi thường xuyên
- Ví dụ: trang blog, landing page.

```tsx
export async function getStaticProps() {
  const res = await fetch("https://api.example.com/posts");
  const posts = await res.json();

  return {
    props: { posts },
    revalidate: 60, // sau mỗi 60 giây, nextjs sẽ re-generate trang
  };
}
```

### getServerSideProps() - SSR

- Lấy dữ liệu mỗi lần có request đến server (server-side rendering).
- Sử dụng khi dữ liệu thay đổi thường xuyên
- Ví dụ: trang blog, landing page.

```tsx
export async function getServerSideProps(context) {
  // context chứa các thông tin của request vừa gọi: query, params, req, res...
  const res = await fetch("https://api.example.com/user", {
    headers: {
      Cookie: context.req.headers.cookie,
    },
  });
  const user = await res.json();

  return {
    props: {
      user,
    },
  };
}
```

### getStaticPaths()

- Build trước nhiều trang động dựa trên dữ liệu
- Sử dụng khi dữ liệu thay đổi thường xuyên
- Ví dụ: trang blog, landing page.

```tsx
export async function getStaticPaths() {
  const res = await fetch("https://api.example.com/posts");
  const posts = await res.json();

  const paths = posts.map((post) => ({
    params: { id: post.id.toString() },
  }));

  return {
    paths,
    fallback: false, // hoặc 'blocking' hoặc true
  };
}
```

## 📡 4. API Routes

- Được đặt trong `/pages/api`, mỗi file tương ứng với một endpoint.

```tsx
// pages/api/user.js
export default function handler(req, res) {
  if (req.method === "GET") {
    // Trả về danh sách users
    res.status(200).json(users);
  }
}
```

## 🎨 5. Head, Metadata

- Dùng next/head để tùy chỉnh tiêu đề trang.

```tsx
import Head from "next/head";

export default function Home() {
  return (
    <>
      <Head>
        <title>Trang chủ</title>
      </Head>
      <h1>Hello</h1>
    </>
  );
}
```

## 🎨 6. Middleware

- Đặt middleware.js ở thư mục gốc để can thiệp request:

```tsx
// middleware.js
import { NextResponse } from "next/server";

export function middleware(request) {
  if (!request.nextUrl.pathname.startsWith("/admin")) return;

  const isLoggedIn = false;
  if (!isLoggedIn) return NextResponse.redirect(new URL("/login", request.url));
}
```
