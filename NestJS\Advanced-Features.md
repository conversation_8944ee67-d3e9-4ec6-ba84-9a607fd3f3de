# Advanced NestJS Features

## 1. Custom Decorators

### Parameter Decorators
```ts
import { createParamDecorator, ExecutionContext } from '@nestjs/common';

// Lấy user từ request
export const GetUser = createParamDecorator(
  (data: unknown, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    return request.user;
  },
);

// Lấy specific field từ user
export const GetUserId = createParamDecorator(
  (data: unknown, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    return request.user?.id;
  },
);

// Sử dụng
@Get('profile')
@UseGuards(AuthGuard)
getProfile(@GetUser() user: User) {
  return user;
}

@Post('posts')
createPost(@GetUserId() userId: number, @Body() dto: CreatePostDto) {
  return this.postService.create(userId, dto);
}
```

### Method Decorators
```ts
import { SetMetadata } from '@nestjs/common';

// Public route decorator
export const IS_PUBLIC_KEY = 'isPublic';
export const Public = () => SetMetadata(IS_PUBLIC_KEY, true);

// Rate limit decorator
export const RATE_LIMIT_KEY = 'rateLimit';
export const RateLimit = (limit: number, ttl: number) => 
  SetMetadata(RATE_LIMIT_KEY, { limit, ttl });

// Sử dụng
@Public()
@Get('health')
healthCheck() {
  return { status: 'ok' };
}

@RateLimit(10, 60) // 10 requests per minute
@Post('send-email')
sendEmail(@Body() dto: SendEmailDto) {
  return this.emailService.send(dto);
}
```

---

## 2. File Upload

### Single File Upload
```ts
import { 
  Controller, Post, UseInterceptors, UploadedFile, 
  ParseFilePipe, MaxFileSizeValidator, FileTypeValidator 
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { diskStorage } from 'multer';

@Controller('upload')
export class UploadController {
  @Post('single')
  @UseInterceptors(FileInterceptor('file', {
    storage: diskStorage({
      destination: './uploads',
      filename: (req, file, cb) => {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, file.fieldname + '-' + uniqueSuffix + '.' + file.originalname.split('.').pop());
      },
    }),
  }))
  uploadFile(
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: 5 * 1024 * 1024 }), // 5MB
          new FileTypeValidator({ fileType: /\.(jpg|jpeg|png|gif)$/ }),
        ],
      }),
    ) file: Express.Multer.File,
  ) {
    return {
      filename: file.filename,
      originalname: file.originalname,
      size: file.size,
      path: file.path,
    };
  }
}
```

### Multiple Files Upload
```ts
@Post('multiple')
@UseInterceptors(FilesInterceptor('files', 5)) // Max 5 files
uploadFiles(@UploadedFiles() files: Express.Multer.File[]) {
  return files.map(file => ({
    filename: file.filename,
    size: file.size,
  }));
}
```

---

## 3. Task Scheduling (Cron Jobs)

### Setup
```bash
npm install @nestjs/schedule
```

### Basic Cron Jobs
```ts
import { Injectable } from '@nestjs/common';
import { Cron, CronExpression, Interval, Timeout } from '@nestjs/schedule';

@Injectable()
export class TasksService {
  // Chạy mỗi ngày lúc 2:00 AM
  @Cron('0 2 * * *')
  handleDailyCleanup() {
    console.log('Running daily cleanup...');
    // Logic cleanup
  }

  // Sử dụng CronExpression enum
  @Cron(CronExpression.EVERY_30_SECONDS)
  handleHealthCheck() {
    console.log('Health check...');
  }

  // Chạy mỗi 10 giây
  @Interval(10000)
  handleInterval() {
    console.log('Running every 10 seconds');
  }

  // Chạy sau 5 giây khi app start
  @Timeout(5000)
  handleTimeout() {
    console.log('Running after 5 seconds');
  }
}

// app.module.ts
@Module({
  imports: [ScheduleModule.forRoot()],
  providers: [TasksService],
})
export class AppModule {}
```

---

## 4. Caching với Redis

### Setup
```bash
npm install @nestjs/cache-manager cache-manager
npm install cache-manager-redis-store redis
```

### Configuration
```ts
import { CacheModule } from '@nestjs/cache-manager';
import * as redisStore from 'cache-manager-redis-store';

@Module({
  imports: [
    CacheModule.register({
      store: redisStore,
      host: 'localhost',
      port: 6379,
      ttl: 600, // 10 minutes
    }),
  ],
})
export class AppModule {}
```

### Usage
```ts
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';

@Injectable()
export class UserService {
  constructor(@Inject(CACHE_MANAGER) private cacheManager: Cache) {}

  async findAll(): Promise<User[]> {
    const cacheKey = 'users_all';
    
    // Kiểm tra cache
    const cached = await this.cacheManager.get<User[]>(cacheKey);
    if (cached) {
      return cached;
    }

    // Lấy từ database
    const users = await this.userRepository.find();
    
    // Lưu vào cache
    await this.cacheManager.set(cacheKey, users, 300); // 5 minutes
    
    return users;
  }

  async clearUserCache() {
    await this.cacheManager.del('users_all');
  }
}
```

### Cache Interceptor
```ts
import { CacheInterceptor } from '@nestjs/cache-manager';

@Controller('users')
@UseInterceptors(CacheInterceptor)
export class UserController {
  @Get()
  @CacheTTL(300) // Override default TTL
  findAll() {
    return this.userService.findAll();
  }
}
```

---

## 5. Rate Limiting

### Setup
```bash
npm install @nestjs/throttler
```

### Configuration
```ts
import { ThrottlerModule } from '@nestjs/throttler';

@Module({
  imports: [
    ThrottlerModule.forRoot([
      {
        ttl: 60000, // 1 minute
        limit: 10,  // 10 requests per minute
      },
    ]),
  ],
})
export class AppModule {}
```

### Usage
```ts
import { Throttle, ThrottlerGuard } from '@nestjs/throttler';

@Controller('auth')
@UseGuards(ThrottlerGuard)
export class AuthController {
  @Post('login')
  @Throttle({ default: { limit: 3, ttl: 60000 } }) // 3 attempts per minute
  login(@Body() dto: LoginDto) {
    return this.authService.login(dto);
  }

  @Post('register')
  @Throttle({ default: { limit: 1, ttl: 300000 } }) // 1 registration per 5 minutes
  register(@Body() dto: RegisterDto) {
    return this.authService.register(dto);
  }
}
```

---

## 6. WebSockets

### Setup
```bash
npm install @nestjs/websockets @nestjs/platform-socket.io socket.io
```

### Gateway
```ts
import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  MessageBody,
  ConnectedSocket,
  OnGatewayConnection,
  OnGatewayDisconnect,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';

@WebSocketGateway({
  cors: {
    origin: '*',
  },
})
export class ChatGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  handleConnection(client: Socket) {
    console.log(`Client connected: ${client.id}`);
  }

  handleDisconnect(client: Socket) {
    console.log(`Client disconnected: ${client.id}`);
  }

  @SubscribeMessage('message')
  handleMessage(
    @MessageBody() data: { room: string; message: string },
    @ConnectedSocket() client: Socket,
  ) {
    // Gửi tin nhắn đến room
    this.server.to(data.room).emit('message', {
      clientId: client.id,
      message: data.message,
      timestamp: new Date(),
    });
  }

  @SubscribeMessage('joinRoom')
  handleJoinRoom(
    @MessageBody() room: string,
    @ConnectedSocket() client: Socket,
  ) {
    client.join(room);
    client.emit('joinedRoom', room);
  }
}
```

---

## 7. Swagger/OpenAPI Documentation

### Setup
```bash
npm install @nestjs/swagger swagger-ui-express
```

### Configuration
```ts
// main.ts
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';

const config = new DocumentBuilder()
  .setTitle('API Documentation')
  .setDescription('The API description')
  .setVersion('1.0')
  .addBearerAuth()
  .build();

const document = SwaggerModule.createDocument(app, config);
SwaggerModule.setup('api', app, document);
```

### DTO Documentation
```ts
import { ApiProperty } from '@nestjs/swagger';

export class CreateUserDto {
  @ApiProperty({ description: 'User name', example: 'John Doe' })
  @IsString()
  name: string;

  @ApiProperty({ description: 'User email', example: '<EMAIL>' })
  @IsEmail()
  email: string;

  @ApiProperty({ description: 'User age', minimum: 18, maximum: 100 })
  @IsInt()
  @Min(18)
  age: number;
}
```

### Controller Documentation
```ts
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';

@ApiTags('users')
@Controller('users')
export class UserController {
  @ApiOperation({ summary: 'Get all users' })
  @ApiResponse({ status: 200, description: 'Users retrieved successfully' })
  @Get()
  findAll() {
    return this.userService.findAll();
  }

  @ApiOperation({ summary: 'Create a new user' })
  @ApiResponse({ status: 201, description: 'User created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiBearerAuth()
  @Post()
  create(@Body() dto: CreateUserDto) {
    return this.userService.create(dto);
  }
}
```

---

## 8. Health Checks

### Setup
```bash
npm install @nestjs/terminus
```

### Health Check Controller
```ts
import { Controller, Get } from '@nestjs/common';
import { HealthCheck, HealthCheckService, TypeOrmHealthIndicator, HttpHealthIndicator } from '@nestjs/terminus';

@Controller('health')
export class HealthController {
  constructor(
    private health: HealthCheckService,
    private db: TypeOrmHealthIndicator,
    private http: HttpHealthIndicator,
  ) {}

  @Get()
  @HealthCheck()
  check() {
    return this.health.check([
      () => this.db.pingCheck('database'),
      () => this.http.pingCheck('external-api', 'https://api.example.com'),
    ]);
  }
}
```

---

## 9. Microservices

### Message Patterns
```ts
import { MessagePattern, ClientProxy } from '@nestjs/microservices';

@Controller()
export class UserController {
  constructor(@Inject('USER_SERVICE') private client: ClientProxy) {}

  @MessagePattern('get_user')
  getUser(data: { id: number }) {
    return this.userService.findById(data.id);
  }

  @Get(':id')
  async getUserHttp(@Param('id') id: number) {
    return this.client.send('get_user', { id }).toPromise();
  }
}
```

---

## 10. Best Practices

### 1. Environment-based Configuration
```ts
// config/configuration.ts
export default () => ({
  port: parseInt(process.env.PORT, 10) || 3000,
  database: {
    host: process.env.DATABASE_HOST,
    port: parseInt(process.env.DATABASE_PORT, 10) || 5432,
  },
  jwt: {
    secret: process.env.JWT_SECRET,
    expiresIn: process.env.JWT_EXPIRES_IN || '1h',
  },
});
```

### 2. Global Exception Filter
```ts
@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse();
    const request = ctx.getRequest();

    const status = exception instanceof HttpException 
      ? exception.getStatus() 
      : 500;

    response.status(status).json({
      statusCode: status,
      timestamp: new Date().toISOString(),
      path: request.url,
      message: exception instanceof Error ? exception.message : 'Internal server error',
    });
  }
}
```

### 3. Request Logging
```ts
@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const { method, url } = request;
    const now = Date.now();

    return next.handle().pipe(
      tap(() => {
        const response = context.switchToHttp().getResponse();
        const { statusCode } = response;
        const delay = Date.now() - now;
        
        console.log(`${method} ${url} ${statusCode} - ${delay}ms`);
      }),
    );
  }
}
```

**Advanced Features Checklist:**
- ✅ Custom decorators cho reusable logic
- ✅ File upload với validation
- ✅ Task scheduling cho background jobs
- ✅ Caching strategy với Redis
- ✅ Rate limiting cho API protection
- ✅ WebSockets cho real-time features
- ✅ API documentation với Swagger
- ✅ Health checks cho monitoring
- ✅ Microservices architecture
