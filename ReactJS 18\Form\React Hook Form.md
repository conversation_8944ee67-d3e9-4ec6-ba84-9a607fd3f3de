# React Hook Form

`https://react-hook-form.com/docs/useform`
`https://viblo.asia/p/react-hook-form-xu-ly-form-de-dang-hon-bao-gio-het-RnB5pAdDKPG`

- `React Hook Form` thông qua việc sử dụng `uncontrolled component` gán vào input bằng cách sử dụng `ref` thay vì phụ thuộc vào `state` để kiểm soát input của bạn. `Tránh re-render`

```tsx
import { useForm, SubmitHandler } from "react-hook-form";

type Inputs = {
  example: string;
  exampleRequired: string;
};

export default function App() {
  // Quản lý 1 Form với `useForm`
  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<Inputs>();

  const onSubmit: SubmitHandler<Inputs> = (data) => console.log(data);

  console.log(watch("example")); // watch: <PERSON><PERSON>t sự thay đổi của biến

  return (
    /* Submit Form với handleSubmit() của useForm */
    <form onSubmit={handleSubmit(onSubmit)}>
      {/* Đăng kí trường gắn vào Input với `register` */}
      <input defaultValue="test" {...register("example")} />
      {/* Thêm validation thông qua `register` */}
      <input {...register("exampleRequired", { required: true })} />
      {/* Hiển thị Error  */}
      {errors.exampleRequired && <span>This field is required</span>}

      <input type="submit" />
    </form>
  );
}
```

## Kết hợp xử lý Validaton với `Yup`

```tsx
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";

// tạo schema để validate
const schema = yup
  .object({
    firstName: yup.string().required(),
    age: yup.number().positive().integer().required(),
  })
  .required();

export default function App() {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema), // resolver: dùng để validate với yup
  });
  const onSubmit = (data) => console.log(data);

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <input {...register("firstName")} />
      <p>{errors.firstName?.message}</p>

      <input {...register("age")} />
      <p>{errors.age?.message}</p>

      <input type="submit" />
    </form>
  );
}
```

## Kết hợp với các thư viện khac với `Controller`

- Khi dùng thư viện bên ngoài như `MaterialUI, Andt, ReactSelect, ReactDapicker` chúng ta sẽ sử dụng Controller để xử lý form.

```tsx
function App() {
  const { handleSubmit, control } = useForm();

  return (
    <form onSubmit={handleSubmit((data) => console.log(data))}>
      <Controller
        control={control}
        name="ReactDatepicker"
        render={({ field: { onChange, onBlur, value, ref } }) => (
          // Gắn các hàm sự kiện của `control` trong useForm vào thư viện ngoài
          <ReactDatePicker
            onChange={onChange}
            onBlur={onBlur}
            selected={value}
          />
        )}
      />

      <input type="submit" />
    </form>
  );
}
```
