# Tài liệu ôn tập SQL & PostgreSQL cho Junior

---

## 1. Tổng quan SQL

- **SQL (Structured Query Language)** là ngôn ngữ thao tác cơ sở dữ liệu quan hệ.
- Dùng để: `<PERSON><PERSON><PERSON>`, `<PERSON><PERSON><PERSON>`, `<PERSON><PERSON><PERSON> vấn`, `<PERSON><PERSON><PERSON> nh<PERSON>t`, `<PERSON><PERSON><PERSON>` dữ liệu.

---

## 2. <PERSON><PERSON><PERSON> lệnh SQL cơ bản

### a. SELECT – Truy vấn dữ liệu

```sql
SELECT name, age FROM users WHERE age > 18;
```

### b. INSERT – Thê<PERSON> dữ liệu

```sql
INSERT INTO users (name, age) VALUES ('<PERSON><PERSON><PERSON>', 25);
```

### c. UPDATE – Cập nhật dữ liệu

```sql
UPDATE users SET age = 26 WHERE name = '<PERSON>uy<PERSON>';
```

### d. DELETE – Xóa dữ liệu

```sql
DELETE FROM users WHERE age < 18;
```

---

## 3. <PERSON><PERSON><PERSON> (CREATE TABLE)

```sql
CREATE TABLE users (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  email VARCHAR(255) UNIQUE,
  age INT DEFAULT 18,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

---

## 4. Ràng buộc dữ liệu (Constraints)

- `PRIMARY KEY`: khóa chính
- `UNIQUE`: không trùng lặp
- `NOT NULL`: không để trống
- `FOREIGN KEY`: khóa ngoại
- `DEFAULT`: giá trị mặc định

---

## 5. Truy vấn nâng cao

### a. JOIN

```sql
-- INNER JOIN
SELECT u.name, o.total
FROM users u
JOIN orders o ON u.id = o.user_id;
```

### b. GROUP BY & HAVING

```sql
SELECT user_id, COUNT(*) AS order_count
FROM orders
GROUP BY user_id
HAVING COUNT(*) > 3;
```

### c. ORDER BY & LIMIT

```sql
SELECT * FROM users ORDER BY created_at DESC LIMIT 10;
```

---

## 6. Subquery

```sql
SELECT name FROM users
WHERE id IN (
  SELECT user_id FROM orders WHERE total > 1000
);
```

---

## 7. Index trong PostgreSQL

```sql
CREATE INDEX idx_users_name ON users(name);
```

> Dùng để tăng tốc truy vấn với WHERE, JOIN, ORDER BY

---

## 8. Transactions

```sql
BEGIN;
UPDATE accounts SET balance = balance - 100 WHERE id = 1;
UPDATE accounts SET balance = balance + 100 WHERE id = 2;
COMMIT;
-- hoặc ROLLBACK;
```

---

## 9. Kiểu dữ liệu phổ biến PostgreSQL

| Kiểu dữ liệu | Mô tả        |
| ------------ | ------------ |
| INTEGER      | Số nguyên    |
| SERIAL       | Tự tăng      |
| VARCHAR(n)   | Chuỗi        |
| TEXT         | Chuỗi dài    |
| BOOLEAN      | TRUE/FALSE   |
| TIMESTAMP    | Ngày giờ     |
| JSON/JSONB   | Dữ liệu JSON |

---
