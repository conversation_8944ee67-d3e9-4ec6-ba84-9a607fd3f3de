# Webpack

`https://itnavi.com.vn/blog/webpack-la-gi`
`https://fullstack.edu.vn/blog/phan-1-tao-du-an-reactjs-voi-webpack-va-babel.html`

Là 1 `task runner` giúp:

- <PERSON><PERSON><PERSON> gói các file thành module và minify `file CSS`, `file JS`
- <PERSON>i<PERSON><PERSON> dịch `SCSS` -> `CSS`
- Tạo một web server dùng cho development.
- <PERSON> dõi những thay đổi và chạy lại các task
- Biên dịch với `Babel`

```js
// webpack.config.js
const path = require("path");
const HtmlWebpackPlugin = require("html-webpack-plugin");

module.exports = {
  entry: "./src/index.js",
  output: {
    path: path.join(__dirname, "/build"),
    filename: "bundle.js",
  },

  // Nơi chứa các loader chịu trách nhiệm tải và gói các tệp nguồn
  module: {
    rules: [
      {
        test: /\.js$/, // Áp dụng cho các thư mục
        exclude: /node_modules/, // Loại trừ thư mục
        use: {
          loader: "babel-loader", // Tích hợp Babel
        },
      },
      {
        // 2 thư viện này giúp webpack có thể tải file .css dưới dạng module.
        // (Nghĩa là có thể import kiểu `import styles from './styles.css';`)
        test: /\.css$/,
        use: ["style-loader", "css-loader"],
      },
    ],
  },
  // Chứa các plugins sẽ cài đặt trong tương lai
  plugins: [
    // Giúp add file `build/bundle.js` vào file `index.html`
    new HtmlWebpackPlugin({
      template: "./public/index.html",
    }),
  ],
};
```

## Cài html-webpack-plugin

- Giúp add `<script src="../build/bundle.js"></script>` tự động vào file `index.html`
- Sau khi chạy webpack file index.html sẽ được thêm file `bundle.js`

```html
<!-- index.html -->
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>React Demo</title>
    <!-- Vị trí được tự động thêm để chạy sever ảo -->
    <script defer src="../build/bundle.js"></script>
  </head>

  <body>
    <div id="root"></div>
  </body>
</html>
```

## Cài webpack-dev-server

- Giúp kết hợp Webpack và Node tạo ra 1 server ảo để dev
