# Operators

- `Operators` là các hàm thuần túy cho phép bạn `biến đổi`, `lọc` và `kết hợp observable`

## Các loại phổ biến

| Nhóm               | <PERSON><PERSON> tả                      | Ví dụ                                      |
| ------------------ | -------------------------- | ------------------------------------------ |
| **Creation**       | Tạo Observable mới         | `of()`, `from()`, `interval()`             |
| **Transformation** | Biến đổi giá trị đầu ra    | `map()`, `scan()`                          |
| **Filtering**      | Lọc giá trị theo điều kiện | `filter()`, `debounceTime()`               |
| **Combination**    | Kết hợp nhiều Observable   | `mergeMap()`, `concatMap()`, `switchMap()` |
| **Utility**        | Hỗ trợ kiểm soát flow      | `tap()`, `take()`, `finalize()`            |
| **Error Handling** | Xử lý lỗi                  | `catchError()`, `retry()`                  |


## Ứng dụng

| Tình huống                     | Nên dùng                     |
| ------------------------------ | ---------------------------- |
| Gọi API khi input thay đổi     | `debounceTime`, `switchMap`  |
| Theo dõi sự kiện form hoặc DOM | `fromEvent`, `map`, `filter` |
| Gộp nhiều request tuần tự      | `concatMap`                  |
| Gộp nhiều request song song    | `mergeMap`                   |
| Xử lý lỗi                      | `catchError`                 |


## Lưu ý

`.pipe()`: là phương thức dùng để kết nối nhiều toán tử (operators) lại với nhau trên một Observable

```tsx
observable.pipe(
  operator1(),
  operator2(),
  ...
).subscribe(...)
```