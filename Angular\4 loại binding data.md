# 4 loại binding data

| Loại Binding            | <PERSON><PERSON> pháp        | <PERSON><PERSON><PERSON> đ<PERSON>                                                                    |
| ----------------------- | -------------- | --------------------------------------------------------------------------- |
| 1. **Interpolation**    | `{{ value }}`  | Hiển thị dữ liệu từ component lên HTML (1 chiều: Component → View)          |
| 2. **Property Binding** | `[prop]="val"` | Gán giá trị từ component vào thuộc tính HTML hoặc directive                 |
| 3. **Event Binding**    | `(event)="fn"` | Gọi hàm trong component khi sự kiện HTML xảy ra (1 chiều: View → Component) |
| 4. **Two-Way Binding**  | `[(ngModel)]`  | Kết hợp property + event binding (2 chiều: Component ⇄ View)                |

```html
<!-- Interpolation -->
<p>Ch<PERSON><PERSON>, {{ username }}!</p>

<!-- Property Binding -->
<img [src]="imageUrl" [alt]="imageAlt" />

<!-- Event Binding -->
<button (click)="sayHello()">Click me</button>

<!-- Two-Way Binding -->
<input [(ngModel)]="username" placeholder="Nhập tên" />

```
📌 Ghi nhớ:
- [property]: từ component vào DOM
- (event): từ DOM vào component
- [(ngModel)]: 2 chiều, cần FormsModule