# So sánh Socket.io và WebSocket

## Socket.io

- `Fallback (dự phòng) kết nối`: Tự động chuyển sang các kết nối khác. (XHR polling, JSONP polling.)
- `Tự động kết nối lại`
- `Room`
- Tạo ra nhiều `overhead` ảnh hưởng hiệu suất mỗi khi chuyển kết nối `Fallback`

## WebSocket `chuẩn RFC 6455`

- `Hiệu suất cao`: WebSocket thiết lập một kết nối TCP duy nhất giảm `overhead`

**`overhead`: là tài nguyên cần bổ sung và sự phức tạp để duy trì tính năng**
