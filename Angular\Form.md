# Form

## 1. FormControl
- <PERSON><PERSON><PERSON> diện cho một trường đơn lẻ
`const nameControl = new FormControl(''); // Khởi tạo với giá trị mặc định là rỗng`

## 2. FormGroup
- Quản lý nhiều trường FormControl
```tsx
const userForm = new FormGroup({
  name: new FormControl(''),
  email: new FormControl(''),
});
```

## 2. FormBuilder
- Giúp tạo và quản lý FormControl và FormGroup dễ dàng hơn
```tsx
import { FormBuilder, FormGroup } from '@angular/forms';
import { Component } from '@angular/core';

@Component({
  selector: 'app-user-form',
  templateUrl: './user-form.component.html',
})
export class UserFormComponent {
  userForm: FormGroup;

  constructor(private fb: FormBuilder) {
    this.userForm = this.fb.group({
      name: [''],
      email: [''],
    });
  }

  onSubmit() {
    if (this.userForm.valid) {
      console.log(this.userForm.value);
    }
  }
}
```
