# Form

## 1. FormControl

- <PERSON><PERSON><PERSON> diện cho một trường đơn lẻ

```tsx
const nameControl = new FormControl(""); // Khởi tạo với giá trị mặc định là rỗng
```

## 2. FormGroup

- Quản lý nhiều trường FormControl

```tsx
const userForm = new FormGroup({
  name: new FormControl(""),
  email: new FormControl(""),
});
```

## 3. FormBuilder

- Giúp tạo và quản lý FormControl và FormGroup dễ dàng hơn

```tsx
import { FormBuilder, FormGroup } from "@angular/forms";
import { Component } from "@angular/core";

@Component({
  selector: "app-user-form",
  templateUrl: "./user-form.component.html",
})
export class UserFormComponent {
  userForm: FormGroup;

  constructor(private fb: FormBuilder) {
    this.userForm = this.fb.group({
      name: [""],
      email: [""],
    });
  }

  onSubmit() {
    if (this.userForm.valid) {
      console.log(this.userForm.value);
    }
  }
}
```

## 4. FormArray

```tsx
import { Component } from '@angular/core';
import { FormBuilder, FormArray, FormGroup, FormControl } from '@angular/forms';

@Component({
  selector: 'app-phone-list',
  templateUrl: './phone-list.component.html',
})
export class PhoneListComponent {
  form: FormGroup;

  constructor(private fb: FormBuilder) {
    this.form = this.fb.group({
      phones: this.fb.array([this.fb.control('')]), // Bắt đầu với 1 trường rỗng
    });
  }

  get phones() {
    return this.form.get('phones') as FormArray;
  }

  addPhone() {
    this.phones.push(this.fb.control('')); // Thêm 1 control mới vào mảng
  }

  removePhone(index: number) {
    this.phones.removeAt(index); // Xóa theo index
  }

  onSubmit() {
    console.log(this.form.value);
  }
}

// HTML
<form [formGroup]="form" (ngSubmit)="onSubmit()">
  <div formArrayName="phones">
    <div *ngFor="let phone of phones.controls; let i = index">
      <input [formControlName]="i" placeholder="Số điện thoại {{ i + 1 }}" />
      <button type="button" (click)="removePhone(i)">Xóa</button>
    </div>
  </div>
  <button type="button" (click)="addPhone()">Thêm số</button>
  <button type="submit">Gửi</button>
</form>
```

## 5. Validators

Sử dụng các validator dựng sẵn:

```ts
this.fb.control("", [Validators.required, Validators.minLength(3)]);
```

- `Validators.required`
- `Validators.minLength`, `maxLength`
- `Validators.email`
- `Validators.pattern`

## 6. Custom Validator

Tạo hàm validator tùy chỉnh:

```ts
// Simple validator
function noSpecialChar(control: AbstractControl): ValidationErrors | null {
  const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(control.value);
  return hasSpecialChar ? { noSpecialChar: true } : null;
}

// Parameterized validator
function minLengthValidator(minLength: number): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    if (control.value && control.value.length < minLength) {
      return {
        minLength: {
          requiredLength: minLength,
          actualLength: control.value.length,
        },
      };
    }
    return null;
  };
}

// Usage
this.form = this.fb.group({
  username: ["", [Validators.required, noSpecialChar]],
  password: ["", [Validators.required, minLengthValidator(8)]],
});
```

---

## 7. Async Validator (Validator bất đồng bộ)

Kiểm tra dữ liệu từ server (VD: kiểm tra email trùng):

```ts
function emailExistsValidator(service: UserService) {
  return (control: AbstractControl) =>
    service
      .checkEmail(control.value)
      .pipe(map((isTaken) => (isTaken ? { emailTaken: true } : null)));
}
```

---

## 8. valueChanges & statusChanges

Lắng nghe sự thay đổi giá trị và trạng thái của form/control:

```ts
this.form.valueChanges.subscribe((value) => console.log(value));
this.form.statusChanges.subscribe((status) => console.log(status));
```

## 9. setValue() vs patchValue()

- `setValue()` yêu cầu cung cấp đầy đủ tất cả control
- `patchValue()` cho phép cập nhật một phần dữ liệu

```ts
this.form.setValue({ name: "A", email: "B" });
this.form.patchValue({ name: "A" }); // không cần đủ
```

---

## 10. reset()

Reset form về trạng thái ban đầu:

```ts
this.form.reset();
```

---
