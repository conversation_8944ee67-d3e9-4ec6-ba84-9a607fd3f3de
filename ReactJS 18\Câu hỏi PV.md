# Câu hỏi phỏng vấn

## Xử lý 2 state gọi lẫn nhau mà không bị lặp vô hạn

- Sử dụng ref để check xem đang `change input` hay `change do setState` để quyết định set lại state đó hay không

```tsx
function Component() {
  const [stateA, setStateA] = useState("");
  const [stateB, setStateB] = useState("");
  const isChangeInput = useRef(false);

  useEffect(() => {
    if (isChangeInput) {
      isChangeInput.current = false;
      setStateB(stateA);
    }
  }, [stateA]);

  useEffect(() => {
    if (isChangeInput) {
      isChangeInput.current = false;
      setStateB(stateA);
    }
  }, [stateA]);

  const handleInputChangeA = (event) => {
    isChangeInput.current = true;
    setStateA(event.target.value);
  };

  const handleInputChangeB = (event) => {
    isChangeInput.current = true;
    setStateB(event.target.value);
  };

  return (
    <div>
      <input type="text" value={stateA} onChange={handleInputChangeA} />
      <input type="text" value={stateB} onChange={handleInputChangeB} />
    </div>
  );
}
```

## So sánh useEffect không truyền mảng depend và không sử dụng useEffect

- Thứ tự chạy sau mỗi lần re-render như sau:
  `log1` -> `DOM update` -> `log2`
- `useEffect` luôn chạy sau khi `DOM update`

```tsx
const MyComponent = () => {
  console.log("console 1");

  useEffect(() => {
    console.log("console 2");
  });

  return <div>{/* Your component JSX */}</div>;
};
```

## Khi nào trong callback của useEffect sử dụng biến mà không cần truyền vào mảng depend

- Biến `global` không thay đổi trong phạm vi của `component` (`Redux`).
- Biến đó được truy cập từ bên ngoài và `không thể thay đổi` trong phạm vi của component (`biến ref`).
- Biến được tạo bên trong `useEffect` và không thay đổi giá trị của biến đó giữa các lần render.

## So sánh useMemo() và useCallback()

### useMemo()

- `useMemo` được sử dụng để memoize một `giá trị tính toán`.

```text
Ví dụ, nếu bạn có một hàm tính toán một giá trị phức tạp và bạn chỉ muốn tính toán giá trị đó lại khi các điều kiện hoặc dependencies thay đổi, bạn có thể sử dụng useMemo để memoize kết quả.
```

- `useCallback` được sử dụng để memoize một `hàm callback`.

```text
Ví dụ, nếu bạn truyền một hàm callback vào một component con và bạn muốn đảm bảo rằng hàm callback đó không bị tạo lại mỗi khi component render lại (ví dụ: khi prop thay đổi), bạn có thể sử dụng useCallback.
```

## So sánh useContext() và Redux

- `useContext()`

```text
+ Sử dụng khi ít có sự thay đổi (theme, language)
+ Là 1 phần của React (k cần cài thư viện)
+ Khi state thay đổi, tất cả các component sử dụng context đều bị re-render (Không cần biết component đó có dùng state đó không)
+ Có thể có nhiều context, nên sẽ lưu trữ state nhiều nơi, khó quản lý
+ Phải Immutable khi thay đổi trạng thái
```

- `Redux`

```text
+ Có sắn thư viên `Immer` hỗ trợ `Mutable` (Chỉ có trong toolkit)
+ Xử lý lưu trữ các dữ liệu phức tạp và trạng thái cập nhập thường xuyên
+ Cần cài đặt
+ Lưu trữ tập trung dữ liệu ở 1 store
+ Middleware (Redux DevTools, redux-thunk, redux-saga)
+ Component chỉ bị re-render khi state nó phụ thuộc thay đổi
+ Kết quả lấy từ selected() được memo hóa tránh re-render
```

## Callback trong useState()

- Sử dụng để `thực thi 1 hàm` mà chỉ muốn chạy `1 lần duy nhất` khi component được mount và trước `useEffect`

```tsx
useState(() => {
  ClientSessionToken.value = inititalSessionToken;
});
```
