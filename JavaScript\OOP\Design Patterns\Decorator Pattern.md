# Decorator Pattern

- <PERSON> ph<PERSON><PERSON> bổ sung hành vi (t<PERSON><PERSON> năng) cho một đối tượng mà không thay đổi code gốc của nó


Nói đơn giản:

❗ Không đụng vào class gốc → nhưng vẫn gắn thêm khả năng mới cho nó (giống như “gắn thêm đồ chơi” 🎮)

```js
// Hàm gốc
function send(message) {
  console.log("Gửi:", message);
}

// Decorator: thêm ghi log trước khi gửi
function withLogging(originalFn) {
  return function (...args) {
    console.log("[LOG] Gọi hàm send với:", args[0]);
    return originalFn(...args);
  };
}

const sendWithLog = withLogging(send);
sendWithLog("Xin chào!");
```

## Ứng dụng

- Là HOC: Hàm nhận 1 function và trả về 1 function