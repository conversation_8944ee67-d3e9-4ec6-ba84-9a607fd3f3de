# CSS3

## Media Queries Responsive

Hỗ trợ tương thích các kích thước màn với Media Queries

```css
/*I<PERSON> ngang(1024 x 768)*/
@media screen and (max-width: 1024px) {
}
/*Ipad dọc(768 x 1024)*/
@media screen and (max-width: 768px) {
}
/*Tablet nhỏ(480 x 640)*/
@media screen and (max-width: 480px) {
}
/*Iphone(480 x 640)*/
@media screen and (max-width: 320px) {
}
/*Smart phone nhỏ*/
@media screen and (max-width: 240px) {
}
```

## Bộ chọn

```css
- p[class^=test] Chọn all <p> với class có giá trị bắt đầu là test
- p[class$=test] Chọn all <p> với class có giá trị kết thúc là test
- p[class*=test] Chọn all <p> với class chứa test
```

## Pseudo-Classes

`:root` trỏ đến phần tử gốc của tài liệu
`:first-child`, `:last-child` lựa chọn phần tử đầu hay cuối cùng của phần tử cha
`:target` thêm định dạng làm nổi bật

## Màu: `RGBA, HSL, HSLA`

## Opacity

## Góc tròn: `border-radius`

## Drop Shadows - Hiệu ứng bóng đổ

## Text Shadow – Bóng văn bản

```text
Linear Gradients – Độ dốc tuyến tính
Radial Gradients – Độ dốc xuyên tâm
Multiple Background Images – Nhiều hình nền
```
