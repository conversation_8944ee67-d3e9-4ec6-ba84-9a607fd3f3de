# Redux

- <PERSON><PERSON> thư viện quả lý State

## I. <PERSON><PERSON> chế hoạt động

![alt](https://images.viblo.asia/3eca7a19-82be-4c9f-8bfc-cbeac838106b.png)

### 3 Thành phần chính

#### 1. Store

- <PERSON><PERSON> kho quản lý trạng thái (Chứa hàm truy cập và quản lý State)
- `dispatch(action)`: G<PERSON>i một `action` đến Redux `store` để xử lý.
- `getState()`: Trả về `state` hiện tại của Redux `store`.
- `subscribe(listener)`: <PERSON><PERSON><PERSON> ký một hàm lắng nghe để được gọi mỗi khi state của Redux thay đổi.

```js
export const store = createStore(counterReducer);
```

#### 2. Action

- Là 1 hàm trả về đối tượng `type` và `payload` đư<PERSON><PERSON> gửi đến `Store` thông qua `Dispatch`
- Ví dụ tạo Action và dispatch Action:

```js
export const increment = (number) => {
  return {
    type: "INCREMENT",
    payload: number,
  };
};

store.dispatch(addItemAction); // Hàm gửi Action đến Store
```

#### 3.Reducer

- Hàm thay đổi trạng thái `State` dựa theo các `Action` được gửi tới
- Ví dụ tạo Reducer:

```js
// Reducer nhận vào State hiện tại và Action. Trả về hành động tương ứng
const initialState = 0;

const counterReducer = (state = initialState, action) => {
  switch (action.type) {
    case "INCREMENT":
      return state + action.payload;
    case "DECREMENT":
      return state - action.payload;
    default:
      return state;
  }
};
```

### Code tổng quát

`https://viblo.asia/p/hoc-react-redux-trong-15-phut-1Je5E7q0ZnL`

- Tạo file chứa các Action
- Tạo file chưa hàm reducers để xử lý Action
- Gom tất cả reduce để truyền vào Store bằng `combineReducer`

```js
const allReducers = combineReducers({
  counter,
  // add more reducers here
});
```

- Tạo Store và Cung cấp cho dự án ở file index.js

```js
import React from "react";
import ReactDOM from "react-dom";
import { createStore } from "redux";
import { Provider } from "react-redux";
import App from "./App";
import allReducers from "./reducers";

// Khởi tạo Store
const store = createStore(allReducers); // truyền reducers đã được combineReducer

ReactDOM.render(
  // truyền store vào Provide toàn dự án
  <Provider store={store}>
    <App />
  </Provider>,
  document.getElementById("root")
);
```

- Sử dụng Redux đã tạo

```js
import { useSelector, useDispatch } from 'react-redux';

const counter = useSelector((state) => state.counter); // Lấy state trong Redux
const dispatch = useDispatch(); // hàm dispatch

handleClick() => {
    dispatch(increment(5)) // Gửi Action
}
```

### Cơ chế

- Tại `View` người dùng click 1 hành động
- `Action` tương ứng được gửi tới `Store` thông qua `Dispatch`
- Trong `Store`, `Reducer` kiểm tra `Action` trong `Dispatch` match với TH nào thì thay đổi `State` theo TH đó
- Trả `State` mới về `View`

## IV. Quy định khi thay đổi Store

- Chỉ thay đổi `store` thông qua `reducers` và `Action` với `dispatch`, không thay đổi trực tiếp `Store`
- Giữ `store` `immutable`

## III. Có 3 loại Redux

### 1. Redux Toolkit

- Hỗ trợ viết Redux nhanh hơn và đơn giản hơn
- Cho dự án nhỏ

### 2. Redux Thunk

- Là `middleware library`
- Cho phép tạo các `Action` trả về 1 hàm thay vì 1 `Object`
- `Dispatch` tới `Action` `hàm` đó bên trong chứa xử lý bất đồng bộ và `Dispatch` tiếp theo các trạng thái

```js
// Tạo Action trả về 1 hầm xử lý bất đồng bộ
export const fetchTrendingRequest = () => {
  return async (dispatch) => {
    dispatch(fetchCountRequest()); // Pending
    try {
      const response = await fetch("https://api.example.com/counter");
      const data = await response.json();
      dispatch(fetchCountSuccess(data.value)); // Fulfilled
    } catch (error) {
      dispatch(fetchCountFailure(error.toString())); // Rejected
    }
  };
};
// Action Pending
export const fetchCountRequest = () => ({ type: "FETCH_COUNT_REQUEST" });
// Action Fulfilled
export const fetchCountSuccess = (count) => ({
  type: "FETCH_COUNT_SUCCESS",
  payload: count,
});
// Action Rejected
export const fetchCountFailure = (error) => ({
  type: "FETCH_COUNT_FAILURE",
  payload: error,
});

// Tạo Reducer
const handleReducer = (state = initialState, action) => {
  switch (action.type) {
    case "FETCH_COUNT_REQUEST":
      return { ...state, loading: true, error: null };
    case "FETCH_COUNT_SUCCESS":
      return { ...state, value: state.value + action.payload, loading: false };
    case "FETCH_COUNT_FAILURE":
      return { ...state, loading: false, error: action.payload };
    default:
      return state;
  }
};

// Dispatch Action
const handleFetch = () => {
  dispatch(fetchTrendingRequest());
};
```

### 3. Redux Saga

- Là middleware library
- Chờ cho `Action` xử lý bất đồng bộ xong mới đừa vào `Dispatch

## III. Tài liệu

`https://techmaster.vn/posts/37636/redux-trong-react-js-huong-dan-day-du`
