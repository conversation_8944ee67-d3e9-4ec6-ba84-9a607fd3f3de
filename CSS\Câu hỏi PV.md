# Câu hỏi PV

## <PERSON><PERSON><PERSON> cách căn giữa Item

- Sử dụng `margin` căn giữa `theo chiều ngang`

```scss
.container {
  margin: 0 auto;
}
```

- <PERSON><PERSON> dụng `flex`

```scss
.container {
  display: flex;
  justify-content: center; // Ngang
  align-items: center; // Dọc
}
```

- Sử dụng `position`

```scss
.container {
  position: relative;
}

..child {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
```

- Sử dụng `grid`

```scss
.container {
  display: grid;
  place-items: center;
}
```

## So sánh `display: none` và `visibility: hidden`

- `display: none`: Không chiếm không gian - Không được render
- `visibility: hidden`: Chiếm không gian - Vẫn được render

## Độ ưu tiên

- 1, `!important`
- 2, `id`
- 3, `class`
- 4, `thẻ`

```text
- Css được viết trực tiếp trong style của thẻ có đội ưu tiên cao nhất
- Class viết sau cùng sẽ ghi đè class trước
```
