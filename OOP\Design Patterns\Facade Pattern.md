# Facade Pattern

- <PERSON><PERSON> cấp `giao diện đơn giản` để tương tác với 1 hệ thống phức tạp hơn
- `<PERSON><PERSON> giấu sự phức tạp` c<PERSON>a hệ thống bên dưới

```js
// Facade
class APIFacade {
  constructor() {
    this.api1 = new API1();
  }

  getData() {
    const data1 = this.api1.getData();
    return { data1 };
  }
}

// API xử lý phức tạp
class API1 {
  getData() {
    // Logic to fetch data from API 1
    return "Data from API 1";
  }
}

// Sử dụng đơn giản. Chỉ cần gọi đến getData() là có dữ liệu
const apiFacade = new APIFacade();
const combinedData = apiFacade.getData();
```

## Ứng dụng

- Giống như `Redux`. Cung cấp `dispatch` và `action`. Che giấu sự phức tạp trong `reduce`. Người dùng chỉ cần `dispatch` các `action` để thay đổi dữ liệu trong `Store`
