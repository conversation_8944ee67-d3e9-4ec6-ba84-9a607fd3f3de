# Position CSS

![alt](https://topdev.vn/blog/wp-content/uploads/2020/09/position-trong-css.png)

## Static

- Mặc định (default), các thành phần sẽ nằm theo thứ tự của văn bản.

## Relative

- Định vị trí tuyệt đối cho các thành phần, không gây ảnh hưởng tới vị trí ban đầu hay các thành phần khác.

## Absolute

- Định vị trí tuyệt đối cho thành phần theo thành phần bao ngoài, hoặc ít nhất là theo cửa sổ trình duyệt.

## Fixed

- Định vị và giúp cho phần tử luôn cố định một chỗ, ví dụ như khi bạn scroll trình duyệt chẳng hạn, phần tử sẽ không thay đổi.

## Sticky

- <PERSON><PERSON><PERSON> dính theo thành phần cha chứa nó
