# Bootstrap

- <PERSON><PERSON> cấp giao diện có sẵn (button, card, ...)
- <PERSON><PERSON><PERSON> tù<PERSON> chỉnh, `cần ghi đè` các class sẵn có thông qua `Sass variables`
- Cần biên dịch lại SCSS sang CSS khi Custom

## Tùy chỉnh

### 1. Tạo `variables.scss` (<PERSON><PERSON><PERSON> định <PERSON>h<PERSON>, khai b<PERSON>o các biến mới)

```scss
// Cách 1: Sử dụng `:root` và `--`)
// gọi biến: var(--primary-color)
:root {
  --primary-color: #1e40af;
  --secondary-color: #ffd700;
}

// Cách 2: Sử dụng $
// gọi biến: $primary-color
$primary-color: #1e40af;
$secondary-color: #ffd700;
```

### 2. Tạo `custom.scss` (Nơi sử dụng, ghi đè và tùy chỉnh)

- Import `variables.scss`
- Import tệp `SASS` của `Bootstrap` vào sau đó ghi đè

```scss
// import tất cả các file SCSS của Bootstrap vào file custom.scss
@import "node_modules/bootstrap/scss/bootstrap";
@import "variables";

// Ghi đè biến mầu mặc định của Bootstrap
$primary: $primary-color; // Sử dụng biến của `$`

p {
  color: var(--secondary-color); // Sử dụng biến của `:root`
}

// Nhúng toàn bộ thay đổi vào CSS của Bootstrap
@import "node_modules/bootstrap/scss/bootstrap";
```

## Sử dụng tùy chỉnh

### 1. Import file `custom.scss` vào `index.js` để sử dụng toàn cầu

### 2. Sử dụng trong component

```js
<button class="bg-primary">Custom Button</button>
```
