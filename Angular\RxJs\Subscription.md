# Subscription

- `Subscription` đại diện cho một mối `li<PERSON><PERSON> kết` giữa `observable` và `observer`
- Sử dụng để `liên kết` (`Subscription`) và `ngăt liên kết` (`unSubscription`)

## Ví dụ

````ts
import { Observable } from "rxjs";

// Tạo một Observable đơn giản
const observable = new Observable((subscriber) => {
  subscriber.next("Hello");
  subscriber.next("World");
  subscriber.complete();
});

// Tạo một Observer
const observer = {
  next: (x) => console.log("Received value: " + x),
  error: (err) => console.error("Something went wrong: " + err),
  complete: () => console.log("Done"),
};

// Đăng ký Observer để nhận dữ liệu từ Observable
observable.subscribe(observer);

// H<PERSON>y đăng ký sau 5 giây
setTimeout(() => {
  observable.unsubscribe();
}, 5000);
```
````
