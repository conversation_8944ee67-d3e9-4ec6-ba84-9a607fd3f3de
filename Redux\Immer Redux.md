# Immer

- Gi<PERSON><PERSON> xử lý Immutable đơn giản hơn

## Immutable là gì ?

- `Immutable`: <PERSON>hi cập nhập 1 giá trị trong object, ta tạo ra 1 object mới và gán lại cho nó thay vì `cập nhập trực tiếp` gọi là `Immutable`

```js
//Nói cách khác: `Immutable` là gán lại 1 object mới để cập nhập
return {
  ...state,
  value: state.value + action.payload,
};
```

- Nếu sử dụng `Immutable` sẽ giúp component dễ dàng nhận ra sự thay đổi để render hơn và ngược lại sẽ khó khăn hơn khi `cập nhập trực tiếp`
- <PERSON><PERSON> nhiên khi `Object` có nhiều lớp thì `Immutable` rất khó

- Immer giúp giải quyết vấn đề: cho phép `cập nhập trực tiếp` nhưng App vẫn nhận biết được sự thay đổi

### 1. Immutable

- Thay đôi giá trị bằng cách thay đổi `tham chiếu`
- Cập nhập Object bằng cách gán 1 Object mới

### 2. Mutable

- Thay đôi giá trị nhưng không làm thay đổi `tham chiếu`
- Cập nhập Object bằng trỏ trực tiếp vào phần tử cần cập nhập
