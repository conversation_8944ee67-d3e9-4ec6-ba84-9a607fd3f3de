# API Server

## I. Server (IO)

### Khởi tạo

- Option : `https://socket.io/docs/v4/server-options/`

```ts
import { createServer } from "http";
import { Server } from "socket.io";

const httpServer = createServer();
const io = new Server(httpServer, {
  // options
});

// IO
io.on("connection", (socket) => {
  // Socket
  socket.on("disconnecting", (reason) => {
    console.log(socket.rooms); // Set { ... }
  });
});

// NameSpace "/admin"
io.of("/admin").emit("hi", { name: "quan" });

httpServer.listen(3000);
```

### Sự kiện

`connection` - <PERSON><PERSON>ch hoạt khi có kết nối từ máy khách.
`new_namespace` − Khi có không gian tên được tạo.

```ts
io.on("connection", (socket) => {
  // ...
});

io.on("new_namespace", (namespace) => {
  console.log(namespace.name);
});
```

### Phương pháp

- `io.adapter()`
- `io.attach()`
- `io.close()`
- `io.disconnectSockets()`
- `io.fetchSockets()`
- `io.path()`
- `io.socketsJoin()`
- `io.socketsLeave()`
- `io.use()`

## II. NameSpace (Không gian tên)

- Biểu diễn cho 1 nhóm Socket được kết nối trong cùng phạm vị bằng tên đường dẫn

```ts
io.of("/admin");
```

### Thuộc tính NameSpace

- `io.of("/admin").adapter`
- `io.of("/admin").sockets` - Bản đồ Socket được kết nối với không gian tên

### Phương pháp NameSpace

- `io.of("/chat").allSockets()` - Trả về danh sách ID Socket kết nôi (Promise)

## III. Socket

- Trả về mỗi khi có Client connect vào IO

### Sự kiện Socket

- `disconnect`
- `disconnecting`

### Thuộc tính Socket

- `socket.handshake`
- `socket.id`
- `socket.recovered`
- `socket.request`

```ts
const handshake = {
  headers: {
    "user-agent": "node-XMLHttpRequest",
    accept: "*/*",
    host: "localhost:3000",
    connection: "close",
  },
  time: "Wed Jan 01 2020 01:00:00 GMT+0100 (Central European Standard Time)",
  address: "::ffff:127.0.0.1",
  xdomain: false,
  secure: false,
  issued: 1577836800000,
  url: "/socket.io/?EIO=4&transport=polling&t=OPAfXv5&b64=1",
  query: {
    EIO: "4",
    transport: "polling",
    t: "OPAfXv5",
    b64: "1",
  },
  auth: {},
};
```

### Phương thức Socket

- `socket.offAny`
- `socket.onAny`
- `socket.prependAny`
