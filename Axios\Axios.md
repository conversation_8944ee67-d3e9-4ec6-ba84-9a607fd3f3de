# Axios

- <PERSON><PERSON> thư viện giúp `Client` tương tác với `Server` thông qua `HTTP` dựa trền `Promises`

## Tính năng chính

### 1. <PERSON><PERSON> dụng `XMLHttpRequest`

- XMLHttpRequest là một đối tượng trong JavaScript giúp lấy dữ liệu từ Server mà không cần tải lại trang

### 2. Hỗ trợ Promise API

- Giúp xử lý bất đông bộ với `.then()` và `.catch()`
- Giúp xử lý lỗi đơn giản với `.catch()`

### 3. Huỷ Request

- Phương thức `signal` giúp hủy `Request`
- Kết hợp với `AbortController` của `WebAPI`

```js
// Hàm set Time sử dụng `AbortController` của `WebAPI`
function newAbortSignal(timeoutMs) {
  const abortController = new AbortController();
  setTimeout(() => abortController.abort(), timeoutMs || 0);

  return abortController.signal;
}

axios
  .get("/foo/bar", {
    signal: newAbortSignal(5000), // Request sẽ được huỷ sau 5 giây
  })
  .then(function (response) {
    // Xử lý khi nhận được Data
  });
```

### 4. Tự động chuyển đổi dữ liệu

- `transformResponse`: cho phép bạn chuyển đổi dữ liệu từ response trước khi nó được trả về cho bạn
- `transformRequest`: cho phép bạn chuyển đổi dữ liệu trước khi gửi nó đi

### 5. `Interceptors trong Axios`

- Giúp xử lý dữ liệu `trước khi gửi` và `sau khi nhận`

- `Request Interceptors`: Được gọi trước khi yêu cầu được gửi đi
- `Response Interceptors`: Được gọi sau khi yêu cầu đã được gửi và phản hồi đã được nhận

```js
import axios from "axios";

// Tạo một `instance` Axios
const instance = axios.create({
  baseURL: "https://api.example.com",
});

// Request Interceptor: Trước khi yêu cầu được gửi đi
instance.interceptors.request.use(
  function (config) {
    // Thực hiện các tác vụ trước khi gửi yêu cầu
    // Ví dụ: Thêm Token
    config.headers.Authorization = "Bearer token123...";
    return config;
  },
  function (error) {
    // Xử lý lỗi request
    return Promise.reject(error);
  }
);

// Response Interceptor: Sau khi nhận phản hồi
instance.interceptors.response.use(
  function (response) {
    // Thực hiện các tác vụ sau khi nhận phản hồi
    // Ví dụ: Xử lý dữ liệu phản hồi
    return response;
  },
  function (error) {
    // Xử lý lỗi Tất cả các loại lỗi response (401, 402, 500, ...)
    return Promise.reject(error);
  }
);

// Sử dụng instance Axios đã tạo
instance
  .get("/data")
  .then(function (response) {
    // Xử lý dữ liệu phản hồi
    console.log(response.data);
  })
  .catch(function (error) {
    // Xử lý lỗi
    console.error("Lỗi:", error);
  });
```

## So sanh khác nhau `Fetch` và `Axios`

- Axios tự động chuyển đổi dữ liệu về dạng Object không cần .json()
- Axios cung cấp Interceptors
- Axios cũng cấp phương thức hủy Request
- Axios không hỗ trợ catching trực tiếp như Fetch
- Fetch được tích hợp sẵn, Axios cần cài đặt

## Tài liệu

`https://200lab.io/blog/axios-la-gi/`
