# Phương thức <PERSON>

`https://www.w3schools.com/jQuery/jquery_ref_overview.asp`

- `$(selector)`: <PERSON><PERSON><PERSON> các thành phần (element) từ HTML bằng `CSS selector`.
- `html()`: <PERSON><PERSON><PERSON> nội dung HMTL hoặc gán giá trị HTML

```js
// Lấy nội dung vào thẻ `p`
$("p").html();

// Gán giá trị vào thẻ `p`
$("p").html("Quân");
```

- `text()`: L<PERSON><PERSON>, thay đổi hoặc gán `text` cho thành phần (element).
- `attr()`: được sử dụng để set hoặc return thuộc tính và giá trị của các phần tử được chọn.
- `addClass()`: Thêm hoặc thay đổi class
- `removeClass()`: Loại bỏ class
- `toggleClass()`: <PERSON><PERSON>t tắt class
- `css()`: Set hoặc trả về `style` của phần tử được chọn
- `show()`: Hi<PERSON><PERSON> thị một phần tử đã bị ẩn.
- `hide()`: Ẩn một phần tử.
- `toggle()`: Ẩn hiện phần tử
- `val()`: Set hoặc return value của các thẻ trong `form`
- `append()`: Nối thêm nội dung vào `cuối phần tử`.
- `prepend()`: Thêm nội dung vào `đầu phần tử`.
- `before()`: Chèn nội dung (có thể có HTML tag) vào trước một phần tử.
- `after()`: Chèn nội dung (có thể có HTML tag) vào sau một phần tử.
- `each()`: Thực hiện một hành động trên từng phần tử.
- `ajax()`: Thực hiện các yêu cầu HTTP không đồng bộ

```js
$(selector).get(URL,data,function(data,status,xhr),dataType)
$(selector).post(URL, data , function(data,status,xhr), dataType)

// Lấy data từ Server và trả về dât cho phần tử được chọn
$(selector).load(URL, data, callback);
```
