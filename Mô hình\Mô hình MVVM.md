# MVVM - Vue.JS

- Tách biệt logic ứng dụng khỏi giao diện
- Thường sử dụng cho `desktop` hoặc `di động`
- Hỗ trợ `data binding` tự động (Cậ<PERSON> nhập `View` khi `Module` thay đổi)

## Model

- `Chức năng`: <PERSON><PERSON><PERSON><PERSON> lý dữ liệu và logic nghiệp vụ của ứng dụng.
- `Tương tác`: Tương tác trực tiếp với cơ sở dữ liệu hoặc các dịch vụ ngoại vi.
- Không có bất kỳ thông tin gì về `View` hay `ViewMode`

## View

- `Chức năng`: Hiển thị dữ liệu từ `ViewModel`.
- `Tương tác`: Nhận dữ liệu từ `ViewModel` và hiển thị cho người dùng.
- <PERSON><PERSON> thuộc: `ViewModel`, sử dụng `data binding` để cập nhập UI

## ViewModel

- `<PERSON>ức năng`: <PERSON><PERSON><PERSON><PERSON> khiển luồng dữ liệu giữa `Model` và `View`.
- `T<PERSON><PERSON>ng tác`: Chuyển đổi dữ liệu từ `Model` sang dạng mà `View` có thể sử dụng, và ngược lại

## Luồng hoạt động

- `View` tương tác trực tiếp với `ViewModel` thông qua `data binding`.
- `ViewModel` xử lý logic giao diện, tương tác với `Model` để lấy hoặc cập nhật dữ liệu.
- `Model` thay đổi thông qua `ViewModel`, và `View` tự động cập nhật khi dữ liệu trong `ViewModel` thay đổi.

## DEMO ReactJS MVVM

- `Module`: State (Redux, Context API), API Services
- `View`: Component render JSX
- `ViewModel`: Component `tách biệt` xử lý logic

```tsx
// Module
export const fetchData = async () => {
  const response = await fetch("https://api.example.com/data");
  const data = await response.json();
  return data;
};
```

```tsx
// ViewModel
const useDataViewModel = () => {
  const [state, setState] = useAppState();

  useEffect(() => {
    const loadData = async () => {
      const data = await fetchData();
      setState({ ...state, data });
    };

    loadData();
  }, [setState]);

  return {
    data: state.data,
  };
};

export default useDataViewModel;
```

```tsx
// View
const DataView = () => {
  const { data } = useDataViewModel(); // Tương tác data binding

  return (
    <div>
      {data.map((item) => (
        <div key={item.id}>{item.name}</div>
      ))}
    </div>
  );
};
```
