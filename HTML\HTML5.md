# HTML5

## HTML5 bổ xung thêm nhiều thẻ đánh dấu (markup)

- `<header>` và `<footer>`
- `<article>` : xác định một phần cụ thể về nội dung
- `<nav>` : x<PERSON>c định khối điều hướng.
- `<section>` : xác định một phần nội dung giống `<div>`
- `<audio>` và `<video>`
- `<embed>`
- `<canvas>` cho phép bạn vẽ đồ họa sử dụng một ngôn ngữ kịch bản riêng biệt. SVG, canvas và những hình dạng vector đều được hỗ trợ bởi HTML5.

## Session Storage & Local Storage

`https://viblo.asia/p/local-storage-session-storage-va-cookie-ORNZqN3bl0n`

### 1. Local Storage

- Khả năng lưu trữ vô thời hạn: <PERSON><PERSON>hĩa là chỉ bị xóa bằng JavaScript, hoặc xóa bộ nhớ trình duyệt, hoặc xóa bằng localStorage API.
- L<PERSON>u trữ được 5MB
- Không gửi thông tin lên server

```tsx
// SET
localStorage.setItem("key", "value");
// hoặc
localStorage.key = "value";
// hoặc
localStorage["key"] = "value";

// GET
localStorage.getItem("key");
// hoặc
localStorage.key;

// Xóa
localStorage.removeItem(key);
// hoặc
localStorage.clear();
```

### 2. Session Storage

- Mất dữ liệu khi đóng tab
- Lưu trữ ít nhất 5MB
- Không gửi thông tin lên server

```tsx
if (typeof Storage !== "undefined") {
  // Khởi tạo sesionStorage
  sessionStorage.setItem("name", "Ted Mosby");
  // get sessionStorage
  sessionStorage.getItem("name");
  // lấy ra số lượng session đã lưu trữ
  sessionStorage.length;
  // xóa 1 item localStorage
  sessionStorage.removeItem("name");
  // xóa tất cả item trong sessionStorage
  sessionStorage.clear();
} else {
  alert("Trình duyệt của bạn không hỗ trợ!");
}
```

### 3. Cookie (Đã tồn tại từ HTML cũ)

- Thông tin gửi lên server mỗi khi tải thông tin
- Có thời gian sống nhất định
- Lưu trữ tối đa 4KB

```tsx
// Khởi tạo
document.cookie = "username=Ted Mosby";
// Get
var x = document.cookie;
// Xóa bằng cách set ngày hết hạn bằng thời điểm đã qua
```

### Chú ý

- `sessionStorage`: giới hạn trong một cửa sổ hoăc thẻ của trình duyệt. Một trang web được mở trong hai thẻ của cùng một trình duyệt cũng không thể truy xuất dữ liệu lẫn nhau. Như vậy, khi bạn đóng trang web thì dữ liệu lưu trong sessionStorage hiện tại cũng bị xóa.
- `localStorage`: có thể truy xuất lẫn nhau giữa các cửa sổ trình duyệt. Dữ liệu sẽ được lưu trữ không giới hạn thời gian.
