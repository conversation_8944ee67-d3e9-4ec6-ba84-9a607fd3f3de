# Closures

`https://viblo.asia/p/tim-hieu-closures-trong-javascript-don-g<PERSON>-<PERSON><PERSON>-Ljy5Vp2zZra`

- `Closure` là một hàm (ghi nhớ) mà có thể truy cập biến thuộc scope chứa nó (hàm closure), ngay cả khi scope chứa nó đã được thực thi xong

```js
function ham_ben_ngoai() {
  var x = 10;

  function ham_ben_trong() {
    console.log(x); // 10
  }

  return ham_ben_trong;
}

let myFunc = ham_ben_ngoai(); // Đã chạy xong
myFunc(); // 10, vẫn lấy được x = 10 để log ra
```

Closure có 3 scope chain, đó là:

- C<PERSON> thể truy cập đến biến của chính nó (biến được định nghĩa trong dấu ngoặc nhọn của nó).
- <PERSON><PERSON> thể truy cập biến của hàm bên ngoài.
- <PERSON><PERSON> thể truy cập biến toà<PERSON> cụ<PERSON> (global).
