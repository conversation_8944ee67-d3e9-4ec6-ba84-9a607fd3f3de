# Signal API
- Trong Angular 16, khi 1 biến được thay đổi Angular sẽ không biết để cập nhập lại giao diện (Chỉ @Input thay đổi nó mới tự động cập nhập toàn bộ). Cần sử dụng ChangeDetectorRef.detectChanges()
- Thay vào đó sử dụng signal(). Nó gần giống useState() trong React. Sẽ tự động cập nhập lại 1 phần giao diện khi có sự thay đổi
- Khi một `signal` thay đổi, component sẽ không re-render toàn bộ. Chỉ `phần giao diện` hoặc `hàm` sử dụng `signal` bị re-render thôi (React sẽ re-render toàn bộ)

```tsx
// Khởi tạo với signal()
counter = signal(0);

// Lấy giá trị của this.counter
console.log(this.counter())

// Cập nhập với update()
this.counter.update(value => value + 1);

// Sử dụng computed() để tính toán lại khi 1 signal phụ thuộc thay đổi
doubleCounter = computed(() => this.counter() * 2);

// Sử dụng effect() để lắng nghe sự thay đổi của các signal tham chiếu bên trong effect và chạy hàm callback
effect(() => {
  console.log('Counter has changed:', this.counter());
});
```
