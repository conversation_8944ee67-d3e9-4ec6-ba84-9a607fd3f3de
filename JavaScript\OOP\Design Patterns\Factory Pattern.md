# Factory Pattern

- `Factory Pattern` giống như một `nhà máy sản xuất đối tượng` – bạn chỉ cần nói muốn loại gì, còn lại nhà máy sẽ tự lo cách tạo ra.

```js
// Định nghĩa một factory function
function createShape(type) {
  if (type === "circle") {
    return new Circle();
  } else if (type === "square") {
    return new Square();
  }
}

// Không cần chỉ định đối tượng (new Circle), chỉ cần truyền type 'circle'
const shape1 = createShape("circle");
```

## Ứng dụng

- Tạo các kiểu `thông báo`, chỉ cần truyền `type` và `message`
