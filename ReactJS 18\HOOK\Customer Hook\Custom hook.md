# Custom Hooks

Custom Hooks cho phép tái sử dụng logic stateful giữa các components.

## 🎯 **Quy tắc Custom Hooks**

1. **Tên bắt đầu với "use"**
2. **<PERSON><PERSON> thể gọi hooks khác bên trong**
3. **Chỉ gọi ở top level của function components**

## 🎯 So s<PERSON>h, phân biệt

### Hàm thông thường

- Không sử dụng Hook
- Không render JSX

### Component

- Phải sử dụng Hook hoặc render JSX

### Custom Hook

- Sử dụng từ khóa `use`
- <PERSON>ên sử dụng Hook
- Không render JSX
