# Luồng Login và Logout

## I. ReactJs

### 1. Login trong ReactJS

- `Clieat` g<PERSON>i username, pass `Server`
- `Server` kiểm tra xem đã tồn tại trong DB chưa
- Nếu có `Server` sinh ra `access_token` và `refresh_token` trả về Cliead đồng thời lưu vào DB
- `Client` nhận và lưu vào `Store` để mỗi khi gửi yêu cầu đền server gắn `access_token` vào `header` để xác thực quyền truy cập `API`

### 2. Logout trong ReactJS

- Client gọi API logout để Server xóa `refresh_token` trong DB
- Client xóa `access_token` và `refresh_token` trong `Store`

### 3. Tự động logout khi access token hết hạn trong ReactJS

**TH gọi đến API**

- Check trong `http` `error 401`
- Gọi logic `logout` nh<PERSON> thông thường

## II. NextJs

### 1. Login trong NextJs

- `Client` gọi đến `Next Server`
- `Next Server` gọi đến `Server BE` trả về `access_token` và `refresh_token`
- `Next Server` set `access_token` và `refresh_token` vào `Cookies` đồng thời trả về cho `Client` lưu vào `Store`

### 2. Logout trong NextJs

- `Client` gọi đến `Next Server`
- `Next Server` gọi đến `Server BE` trả về thành công
- `Next Server` set `access_token` và `refresh_token` trong `Cookies` thành rỗng đồng thời trả về thành công cho `Client`
- `Client` xóa `access_token` và `refresh_token` trong `Store`

### 3. Tự động logout khi `access_token` hết hạn trong NextJs

**TH gọi đến API**

- Check trong `http` `error 401`
- Gọi logic `logout` như thông thường

**TH reload trang**

- Check `Cookies` trong `middware.ts`
- Khi `access_token` hết hạn sẽ redirect đến `/logout?refresh_token=`
- Tại `/logout?refresh_token=` sẽ check `refresh_token` có đúng không (tránh tự truy cập vào `/logout`) sau đó gọi logic `logout` như thông thường

### 4. Cơ chế refreshToken

- `refresh` trước khi `accessToken` hết hạn
- Sử dụng `setInterval`
