# i18n

- <PERSON><PERSON> dụng tạo đa ngôn ngữ cho web

## 1. Khởi tạo file ngôn ngữ

```json
// File src/locales/en/translation.json
{
  "common": {
    "button": {
      "search": "Search",
      "cancel": "Cancel",
      "delete": "Delete",
      "save": "Save"
    }
  }
}
```

## 2. Config ngôn ngữ

```js
import i18next from "i18next";
import { initReactI18next } from "react-i18next";
import translationEN from "../locales/en/translation.json";
import translationVI from "../locales/vi/translation.json";

const resources = {
  en: { translation: translationEN },
  vi: { translation: translationVI },
};

i18next.use(initReactI18next).init({
  lng: "en",
  debug: true,
  resources,
});
```

## 2. Import file Config trên vào `index.js`

```js
import "./config-translation"; // <=== thêm file Config ngôn ngữ
import React from "react";
import ReactDOM from "react-dom/client";
import "./index.css";
import App from "./App";

const root = ReactDOM.createRoot(document.getElementById("root"));
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);
```

## 3. Sử dụng

```js
import { useTranslation } from "react-i18next";

export default function App() {
  const { t } = useTranslation();

  return (
    <div className="container text-center">
      <span>{t("common.button.search")}</span>
      <span>{t("common.button.cancel")}</span>
      <span>{t("common.button.delete")}</span>
      <span>{t("common.button.save")}</span>
    </div>
  );
}
```

## 4. Xử lý Button chuyển ngôn ngữ

```js
import { useTranslation } from "react-i18next";
import { useState } from "react";

export default function App() {
  const { t, i18n } = useTranslation();
  const [currentLanguage, setCurrentLanguage] = useState("en"); // State lưu trạng thái ngôn ngữ

  const handleChangeLanguage = () => {
    setCurrentLanguage(currentLanguage === "en" ? "vi" : "en"); // Set State
    i18n.changeLanguage(currentLanguage === "en" ? "vi" : "en"); // Update ngôn ngữ qua `i18n`
  };

  return <button onClick={handleChangeLanguage}>Change</button>;
}
```

## Tài liệu

`https://viblo.asia/p/setup-da-ngon-ngu-i18n-trong-reactjs-018J29w04YK`
