# Thay đổi DOM

`https://galaxyz.net/cach-thuc-hien-thay-doi-doi-voi-dom.2681.anews`

## Tạo nút mới

- `createElement()` - Tạo một nút phần tử mới
- `createTextNode()` - Tạo một nút văn bản mới
- `node.textContent` - Lấy hoặc đặt nội dung văn bản của một nút phần tử
- `node.innerHTML` - Lấy hoặc đặt nội dung HTML của một phần tử (Rủi ro XSS)

```tsx
const paragraph = document.createElement("p");
paragraph.textContent = "I'm a brand new paragraph.";
paragraph.innerHTML = "I'm a paragraph with <strong>bold</strong> text.";
```

## Chèn các node vào DOM

- `node.appendChild()` - Thêm một nút làm nút con cuối cùng của phần tử mẹ
- `node.insertBefore()` - <PERSON><PERSON><PERSON> một nút vào phần tử mẹ trước một nút anh em được chỉ định
- `node.replaceChild()` - Thay thế một nút hiện có bằng một nút mới

## Xóa các node khỏi DOM

- `node.removeChild()`- Xóa nút con
- `node.remove()`- Xóa nút
