# Amazon SES (<PERSON><PERSON><PERSON> Mail số lượng lớn)

`https://duthanhduoc.com/blog/huong-dan-gui-email-voi-aws-ses-va-nodejs`

- Gửi mail số lượng lớn
- Chi phí rẻ

## B<PERSON>ớc thực hiện

- Tạo SES trên trang Amazon sẽ nhận được `Access key` và `Secret access key`

- Setup file `.env`

```bash
AWS_ACCESS_KEY_ID='AKPPPPLSKDJFLKASDFJLK' # Access key
AWS_SECRET_ACCESS_KEY='UUQMLtlghDpf4jJLIFJAOSDFJOLYNOAqweyhu' # Secret access key
AWS_REGION='ap-southeast-1' # Khi tạo SES trên web
SES_FROM_ADDRESS='<EMAIL>' # Địa chỉ email gửi đi
```

- Cài `@aws-sdk/client-ses`

- Tạo file `send-email.js`

```js
const { SendEmailCommand, SESClient } = require("@aws-sdk/client-ses");
const { config } = require("dotenv");

config();
// Tạo dịch vụ SES
const sesClient = new SESClient({
  region: process.env.AWS_REGION,
  credentials: {
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  },
});

// Khởi tạo SendEmailCommand dựa trên thư viện `@aws-sdk/client-ses`
const createSendEmailCommand = ({
  fromAddress,
  toAddresses,
  ccAddresses = [],
  body,
  subject,
  replyToAddresses = [],
}) => {
  return new SendEmailCommand({
    Destination: {
      /* required */
      CcAddresses: ccAddresses instanceof Array ? ccAddresses : [ccAddresses],
      ToAddresses: toAddresses instanceof Array ? toAddresses : [toAddresses],
    },
    // Nội dung mail
    Message: {
      Body: {
        Html: {
          Charset: "UTF-8",
          Data: body,
        },
      },
      Subject: {
        Charset: "UTF-8",
        Data: subject,
      },
    },
    Source: fromAddress,
    ReplyToAddresses:
      replyToAddresses instanceof Array ? replyToAddresses : [replyToAddresses],
  });
};

const sendVerifyEmail = async (toAddress, subject, body) => {
  const sendEmailCommand = createSendEmailCommand({
    fromAddress: process.env.SES_FROM_ADDRESS,
    toAddresses: toAddress,
    body,
    subject,
  });

  try {
    return await sesClient.send(sendEmailCommand);
  } catch (e) {
    console.error("Failed to send email.");
    return e;
  }
};

// Thực hiện gửi
sendVerifyEmail(
  "<EMAIL>",
  "Tiêu đề email",
  "<h1>Nội dung email</h1>"
);
```

## Quy trình code

- Khởi tạo SendEmailCommand `(Cấu trúc email)`
- Sử dụng sesClient.send() để gửi email
