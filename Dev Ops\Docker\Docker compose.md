# Docker Compose

- Tạo 1 file định nghĩa cấu hình `container` có tên `docker-compose.yml`.
- Thay vì chạy nhiều lệnh để run `container`, chỉ cần gọi Docker Compose để chạy 1 lần duy nhất
- Setting `Image`, `Post`, `Volumes`

```bash
# Trước khi dùng Docker compose
docker container run -dp 4000:4000 -v ~/Documents/DuocEdu/NodeJs-Super/Twitter/uploads:/app/uploads anhquan219/my-app:v0

# Sau khi dùng Docker compose
docker-compose up
```

## Cấu trúc file

```yml
# docker-compose.yml
version: "3"
services:
  twitter: # Tên services (tên gì cũng được)
    image: anhquan219/my-app:v0 # Image để tạo container
    ports: # Post vào và ra
      - "3000:3000"
    volumes: # volumes nếu có
      - "~/Documents/DuocEdu/NodeJs-Super/Twitter/uploads:/app/uploads"
```
