# Zod

- Ph<PERSON> biến cho dự án TypeScript (NextJS)
- Thêm kiểm tra tùy chỉnh với `refine`

```tsx
import { z } from "zod";

const UserSchema = z.object({
  name: z.string(),
  age: z.number().refine((age) => age >= 0 && age <= 120, {
    message: "Age must be between 0 and 120",
  }),
});

const userData = { name: "<PERSON>", age: 30 };

// Xử lý lỗi với `safeParse`
const result = UserSchema.safeParse(userData);
if (!result.success) {
  console.log("Lỗi");
}

// X<PERSON> lý lỗi với `parse`
try {
  const parsedData = UserSchema.parse(userData);
  console.log(parsedData);
} catch (error) {
  console.error("Lỗi", error);
}
```

## Chức năng xác thực

- `safeParse`: Lỗi trả về trong kết quả. G<PERSON><PERSON> ra khi cần xử lý
- `parse`: tự động ném ra lỗi. Cần sử dụng trong `try catch`
