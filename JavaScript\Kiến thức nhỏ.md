# Kiến thức nhỏ

## 1. So sánh `==` và `===`

- `==`: So sánh giá trị, có ép kiểu nếu cần (lỏng lẻo)
- `===`: So sánh cả giá trị và kiểu, không ép kiểu (nghiêm ngặt)

```js
// Ví dụ về ==
5 == "5"; // true (string "5" được chuyển thành number 5)
true == 1; // true (true được chuyển thành 1)
null == undefined; // true

// Ví dụ về ===
5 === "5"; // false (khác kiểu: number vs string)
true === 1; // false (khác kiểu: boolean vs number)
null === undefined; // false
```

## 2. So sánh `||` và `??`

`string rỗng là false`

- `||`: Trả về giá trị đầu tiên là `true`. Nếu tất cả đều `false` thì lấy giá trị cuối cùng

```js
let b = false || true; // true
let c = 3 || 5; // 3
let a = 0 || 1; // 1
let a = "" || 1; // 1
```

- `??`: Trả về giá trị đầu tiên nếu không phải `null` hoặc `undefined`. Nếu tất cả không thỏa mãn thì lấy giá trị cuối cùng

```js
let x = null ?? 1; // 1
let y = undefined ?? 1; // 1
let z = 0 ?? 1; // 0
let z = "" ?? 1; // ""
```

## 3. Strict mode

`https://viblo.asia/p/tim-hieu-ve-strict-mode-trong-javascript-jaqG0QQevEKw`

`"use strict";`

- Ngăn sử dụng tên trường có thể làm keywork trong tương lại
- Ngăn chặn các tính năng có thể gây nhầm lẫn
- Không thể sử dụng 1 biến mà không khai báo từ kháo (const, let, var)

## 4. Bất đồng bộ (async/await) trong For và ForEach

`https://spiderum.com/bai-dang/Vong-lap-trong-Javascript-Lam-cach-nao-su-dung-asyncawait-alr`

- `For`: Chạy tuần tự và `có` chờ Promise
- `ForEach`: Chạy tuần tự nhưng `không` chờ Promise
- Không sử dụng `bất đồng bộ` trong `forEach` đc vì `forEach` không đợi cho đến khi các phần tử trong mảng được truyền hết
- Nên dùng `for of` cho xử lý async loop

![alt](https://images.spiderum.com/sp-images/cd1e33006f2d11e885f7a31ea4d8c9aa.png)

## 4. `3 Cách Clone Object`

- Clone lông:
  Sử dụng `...`
  Sử dụng `Object.assign()`
- Clone sâu:
  Sử dụng: `JSON`
  Sử dụng: `cloneDeep` của lodash
- Lưu ý: Sử dụng: `JSON` không clone được `method`

## 5. map() và forEach()

`https://viblo.asia/p/tai-sao-va-khi-nao-nen-su-dung-foreach-map-filter-reduce-va-find-trong-javascript-naQZRYwmKvx`

- `map()`: Trả về mảng mới, tốc độ nhanh hơn
- `forEach()`: Không trả về gì, có thể thay đổi dữ liệu từng phần tử lọc qua

## 6. `Tham chiếu` và `Tham trị`

### Tham trị (value type)

- Lưu trữ giá trị
- Khi gán sẽ tạo 1 bản sao gán vào biến mới. Khi biến gốc thay đổi không ảnh hưởng đến biến được gán
- Kiểu dữ liệu: `number, string, boolean, null, undefined`

### Tham chiếu (Refenrence type)

- Lưu trữ địa chỉ
- Khi gán sẽ tham chiếu đến ô nhớ gốc. Khi biến gốc thay đổi sẽ ảnh hưởng đến biến được gán
- Kiểu dữ liệu: array, object

## 7. Null, Undefined, NaN

### Undefined

- Có nghĩa là không xác định
- type: `undefined`
- Trong toán học: trả về `NaN`

```js
// khi bạn khai báo một biến nhưng chưa gán giá trị
var test; // undefined
```

### Null

- Là 1 object không tồn tại địa chỉ hoặc không có giá trị
- type: `object`
- Trong toán học: trả `0`

```js
// Sử dụng để gán cho 1 biến đại diện không có giá trị
var test = null;
```

### NaN

- Nghĩa là không phải 1 số
- type: `number`

## 8. var, let, const

- `var`: global scope, được update và được khai báo lại
- `let`: block scope, được update nhưng không khai báo lại
- `const`: block scope, không update và không khai báo lại
