# Currying function

- <PERSON><PERSON> phép biến đổi 1 hàm có thể gọi theo dạng `f(a, b, c)` thành `f(a)(b)(c)`

```js
// một hàm sum thông thường
const sum = (a, b) => a + b;

// Currying function
const curriedSum = function (a) {
  return function (b) {
    return a + b;
  };
};

// Currying với arrow function
const curriedSum = (a) => (b) => a + b;

//gọi hàm cà-ri
curriedSum(4)(5);
```

- <PERSON><PERSON> dụ thực tế tái sử dụng

```js
const findNumberByCondition = (num) => (func) => {
  const result = [];
  for (let i = 0; i < num; i++) {
    if (func(i)) {
      result.push(i);
    }
  }
  return result;
};

console.log(findNumberByCondition(10)((num) => num % 2 !== 0));
//-->Output: [1, 3, 5, 7, 9]

console.log(findNumberByCondition(20)((num) => num % 2 === 0));
//-->Output: [0, 2, 4, 6, 8, 10, 12, 14, 16, 18]

console.log(findNumberByCondition(30)((num) => num % 3 === 2));
//-->Output: [2, 5, 8, 11, 14, 17, 20, 23, 26, 29]
```
