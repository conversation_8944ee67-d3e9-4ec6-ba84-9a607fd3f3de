# Làm việc với  Browser

## 1. <PERSON><PERSON><PERSON> tiếp giữa các Tab bằng `BroadcastChannel`

- Tạo 1 kênh truyền thông để gửi và nhận dữ liệu giữa các Tab không cần Serve

```js
// Tạo kênh truyền thông với tên 'example_channel'
const channel = new BroadcastChannel("example_channel");

// Hàm gửi dữ liệu
const handleClick = () => {
  const message = "Hello from tab A";
  channel.postMessage(message);
};

// Nhận dữ liệu từ các Tab khác
useEffect(() => {
  channel.onmessage = (event) => {
    console.log("Received message from other tab:", event.data);
  };

  // Dọn dẹp khi component unmount
  return () => {
    channel.close();
  };
}, []);

```

### Lưu ý
- `BroadcastChannel` chỉ hoạt động khi các tab nằm cùng origin (cùng domain + protocol + port).
- Không hỗ trợ trên tất cả các trình duyệt cũ (ví dụ: IE, một số trình duyệt di động cũ).
- Không hoạt động giữa tab và iframe khác origin.

## 2. Bắt sự kiện User Focus hoặc rời tab với `visibilitychange`

- `"visible"`: Focus Tab
- `"hidden"`: Rời Tab

```js
import { useEffect } from "react";

export default function App() {
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === "visible") {
        // TODO: Gọi API kiểm tra token hoặc session
        // if (!isAuthenticated()) logout();
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);

    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, []);

  return (
    <div className="App">
      <h1>Hello CodeSandbox</h1>
    </div>
  );
}

```


