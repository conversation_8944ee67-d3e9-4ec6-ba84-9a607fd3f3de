# MongoDB

- <PERSON><PERSON> một cơ sở dữ liệu `NoSQL`, nghĩa là nó không sử dụng `bảng` và `hàng` như `cơ sở dữ liệu quan hệ`
- Sử dụng các `collections` và `documents`

  `Documents`: Các tài liệu trong MongoDB được lưu trữ dưới dạng JSON (thường là BSON - Binary JSON) và không cần tuân thủ cấu trúc cố định.
  `Collections`: Tập hợp các tài liệu tương tự như bảng trong cơ sở dữ liệu quan hệ nhưng `không có cấu trúc cố định`.

## Các lệnh cơ bản

- `Insert`: `db.collection.insertOne(document)` hoặc db.collection.insertMany(documents)
- `Query`: `db.collection.find(query)`
- `Update`: `db.collection.updateOne(filter, update)` hoặc db.collection.updateMany(filter, update)
- `Delete`: `db.collection.deleteOne(filter)` hoặc db.collection.deleteMany(filter)

## Cấu trúc dữ liệu

- Không cần định nghĩa dữ liệu trước khi lưu
- Có thể nhúng tài liệu bên trong tài liệu khác

## Chỉ múc (Indexes)

- Indexes: Sử dụng để tăng tốc độ truy vấn. Bạn có thể tạo chỉ mục trên một hoặc nhiều trường.
