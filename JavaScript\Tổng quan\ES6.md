# ES6 (ECMAScript 2015)

## 1. `var`, `let`, `const`

- N<PERSON><PERSON> biến đư<PERSON> khai báo trong 1 Scope thì biến đó chỉ sử dụng được trong Scope đó

| Từ khóa | Phạm vi (scope) | <PERSON><PERSON> l<PERSON> | Hoisting      | Ghi chú                       |
| ------- | --------------- | ------- | ------------- | ----------------------------- |
| `var`   | Function        | ✅       | ✅ (undefined) | Có thể gây lỗi logic          |
| `let`   | Block           | ✅       | ❌             | Không dùng khi chưa khai báo  |
| `const` | Block           | ❌       | ❌             | Phải gán giá trị ngay lập tức |

## 2. Arrow Functions

- Không có `context` riêng (Sử dụng `context` của hàm gần nhất chứa nó) => `this` của <PERSON> Functions lấy `this` phạm vị bên ngoài gần nhất chứa nó
- Không thể được sử dụng như là `constructor` để tạo ra các đối tượng mới.

## 3. Default Parameter Values

```js
function greet(name = "Guest") {
  return `Hello, ${name}`;
}
```

## 4. Destructuring và toán tử `...`

```ts
const { firstName, lastName } = obj;
```

## 5. Template literals

```js
console.log(`Xin chao ${name}`);
```


## 6. Promise

```tsx
const p = new Promise((resolve, reject) => {
  setTimeout(() => {
    resolve("Done");
  }, 1000);
});

p.then((data) => console.log(data)).catch((err) => console.error(err));
```

## 7. Map và Set

- `Map`: Lưu dữ liệu cấu trúc `key-value`, `key` là `unique`

```ts
let myMap = new Map();

myMap.set("key", "value");

console.log(myMap.get("key")); // output: "value"
console.log(myMap.size); // output: 1
myMap.delete("key");
```

- `Set`: Lưu dữ liệu không trung lặp

```ts
let mySet = new Set();

mySet.add(1);
mySet.add(2);
mySet.add(2); // Giá trị 2 sẽ không được thêm vào do Set không chấp nhận giá trị trùng lặp

console.log(mySet.size); // output: 2
mySet.delete(2);
```

## 8. Multi-line Strings
## 9. For...of
## 10. Module (import/export)
