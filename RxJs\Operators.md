# Operators

- `Operators` là các hàm thuần túy cho phép bạn `biến đổi`, `lọ<PERSON>` và `kết hợp observable`

## Các loại phổ biến

- `Creation Operators`: Tạo observable (`of`, `from`, `interval`).
- `Transformation Operators`: <PERSON><PERSON><PERSON><PERSON> đổi giá trị (`map`, `scan`).
- `Filtering Operators`: <PERSON><PERSON><PERSON> gi<PERSON> trị (`filter`, `debounceTime`).
- `Combination Operators`: <PERSON><PERSON><PERSON> hợ<PERSON> nhiề<PERSON> observable (`mergeMap`, `concatMap`).
