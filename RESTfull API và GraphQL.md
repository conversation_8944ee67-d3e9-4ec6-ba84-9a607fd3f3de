# RESTfull API và GraphQL

- C<PERSON>c kiến trúc thiết kế API

## RESTfull API

- Sử dụng đầy đủ `Method HTTP`
- Không chỉ định cụ thể dữ liệu, gây thừa dữ liệu
- Tài nguyên được định danh bởi các `URL` => nhiều `endpoint`
- <PERSON>ữ liệu trả về do Server chỉ định với từng `endpoint` (Cứng nhắc)

## GraphQL

- Ngôn ngữ truy vấn dữ liệu
- Chỉ định cụ thể dữ liệu nhận về
- Chỉ có 1 `endpoint` cho mỗi loại dữ liệu
- Cấu trức data trả về như format `query` gửi lên
- Chỉ dùng Method `POST` đặt `query` trong `body`

```text
- query: Lấy dữ liệu (GET trong REST API)
- Mutation: Thêm/sửa/xóa (POST/PUT/DELETE trong REST API)
- Subscription: lắng nghe sự thay đổi trên Server
```

```ts
const query = `
    query {
      user(id: "123") {
        name
        email
      }
    }
  `;

const data = await fetch("https://api.example.com/users", {
  method: "POST",
  headers: {
    "Content-Type": "application/json",
    // Có thể cần phải thêm các header khác như authorization token tại đây nếu có
  },
  body: JSON.stringify({ query }),
});
```
