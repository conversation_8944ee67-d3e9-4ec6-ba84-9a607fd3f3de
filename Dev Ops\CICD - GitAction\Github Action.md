# Github Action

`https://edu.duthanhduoc.com/learn/NodeJs-Super?lessonId=356`

- Tạo `Github Action` trên web Github với môi trường `Docker`
- <PERSON><PERSON> dụng các `toolkit` của `Github Action`
- Tạo `Action secrect` trong `Setting`
  <PERSON>ài khoản Docker `secrets.DOCKERHUB_USERNAME`
  Mật khẩu Docker `secrets.DOCKERHUB_PASSWORD`
  Post đầu ra khi build `secrets.POST`
  File .env `secrets.ENV`

`.github/workflows/node.js.yml`

```yml
name: Node.js CI

# on: Setup câu lệnh và branches muốn kích hoạt CICD
on:
  push: # Khi Push code trực tiếp vào nhánh main
    branches: ["main"]
  pull_request: # Khi tạo pull (merge) vào nhánh main
    branches: ["main"]

# Những công việc
jobs:
  build: # Tên, c<PERSON> thể thay đổi tùy ý
    runs-on: ubuntu-latest # Chạy trên máy ảo này

    # <PERSON><PERSON><PERSON> b<PERSON><PERSON>c thực hiện
    steps:
      - uses: actions/checkout@v4 # Kéo code từ git vào máy ảo `ubuntu` đã setup
      - name: "Create env file"
        run: echo "${{ secrets.ENV }}" > .env.production
      - name: Build Docker Image tu Dockerfile
        run: docker build --progress=plain -t anhquan219/my-app:v0 .
      - name: Login Docker Hub
        run: docker login -u ${{ secrets.DOCKERHUB_USERNAME }} -p ${{ secrets.DOCKERHUB_PASSWORD }}
      - name: Push Docker image to Docker Hub
        run: docker push anhquan219/my-app:v0

  deploy:
    runs-on: ubuntu-latest
    needs: build

    steps:
      - name: "Executing remote ssh commands using password"
        uses: appleboy/ssh-action@v1.0.0
        # Tham số VPS (Truy cập vào VPS và pull Image từ Docker Hub về chạy Contianer)
        with:
          host: ${{ secrets.HOST}}
          username: ${{ secrets.HOST_USERNAME}}
          password: ${{ secrets.HOST_PASSWORD}}
          post: ${{ secrets.POST}}
          # Các câu lệnh thực thi trên VPS
          script: |
            docker login -u ${{ secrects.DOCKERHUB_USERNAME }} -p ${{ secrects.DOCKERHUB_PASSWORD }}
            docker pull anhquan219/my-app:v0
            docker stop my-container
            docker rm my-container
            docker container run -dp 3000:3000 --name my-container anhquan219/my-app:v0
```
