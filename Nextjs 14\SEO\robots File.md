# robots.txt

`https://nextjs.org/docs/app/api-reference/file-conventions/metadata/robots#static-robotstxt`

- <PERSON><PERSON><PERSON> cho công cụ tìm kiếm biết nên truy cập và không nên truy cập vào file nào

## Sử dụng trong Next

```ts
// /app/robots.ts
import { MetadataRoute } from "next";

export default function robots(): MetadataRoute.Robots {
  return {
    rules: {
      userAgent: "*", // Áp dụng cho mọi bot
      allow: "/", // Cho phép truy cập toàn site
      disallow: "/private/", // Không cho truy cập thư mục `private`
    },
    sitemap: `https://acme.com/sitemap.xml`, // Đường dẫn tới sitemap
  };
}
```

## File được Generate ra cho công cụ tìm kiếm đọc

```txt
User-Agent: *
Allow: /
Disallow: /private/

Sitemap: https://acme.com/sitemap.xml
```

## Ghi chú

- Không hoạt động nếu dùng `Page Router` (phải tự tạo public/robots.txt).
