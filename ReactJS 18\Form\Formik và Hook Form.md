# 🔍 So sánh: Formik vs React Hook Form

| Tiêu chí                       | Formik                                             | React Hook Form                                           |
|--------------------------------|----------------------------------------------------|-----------------------------------------------------------|
| 🧠 Phương pháp quản lý         | React state                                        | ref                                                       |
| ⚡️ Hiệu năng                   | Chậm do nhiều re-render                            | Tối ưu, ít re-render                                      |
| 🧩 Hỗ trợ validation           | Tốt (Yup tích hợp sẵn)                             | Tốt (Yup, Zod, custom schema đều được)                   |
| 📁 Mảng                        | Sử dụng FieldArray (dài dòng)                      | useFieldArray đơn giản hơn                               |
| 🔧 Công cụ hook hỗ trợ         | useFormik, useField, FormikContext                 | useForm, useController, useFieldArray                    |

# React Hook Form

- Sử dụng `ref` thay vì `state` để liên kết dữ liệu input `(uncontrolled components)` ➜ giảm `re-render`

```tsx
import { useForm, SubmitHandler } from "react-hook-form";

export default function App() {
  // Quản lý 1 Form với `useForm`
  const {
    register,        // Gắn input
    handleSubmit,    // Submit handler
    watch,           // Theo dõi giá trị input
    formState: { errors } // Trạng thái lỗi
  } = useForm();

  const onSubmit: SubmitHandler = (data) => console.log(data);

  console.log(watch("example")); // watch: Bắt sự thay đổi của biến

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      {/* Đăng kí trường gắn vào Input với `register` */}
      <input defaultValue="test" {...register("example")} />
      {/* Thêm validation thông qua `register` */}
      <input {...register("exampleRequired", { required: true })} />
      {/* Hiển thị Error  */}
      {errors.exampleRequired && <span>This field is required</span>}

      <input type="submit" />
    </form>
  );
}
```

## Kết hợp xử lý Validaton với `Yup`

```tsx
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";

// tạo schema để validate
  const schema = yup.object({
    firstName: yup.string().required(),
    age: yup.number().positive().integer().required(),
  })
  .required();

export default function App() {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema), // resolver: dùng để validate với yup
  });
  const onSubmit = (data) => console.log(data);

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <input {...register("firstName")} />
      <p>{errors.firstName?.message}</p>

      <input {...register("age")} />
      <p>{errors.age?.message}</p>

      <input type="submit" />
    </form>
  );
}
```

## Kết hợp với các thư viện khac với `Controller`

- Khi dùng thư viện bên ngoài như `MaterialUI, Andt, ReactSelect, ReactDapicker` chúng ta sẽ sử dụng `Controller` để xử lý `form`.

```tsx
function App() {
  const { handleSubmit, control } = useForm();

  return (
    <form onSubmit={handleSubmit((data) => console.log(data))}>
      <Controller
        name="ReactDatepicker"
        control={control}
        render={({ field: { onChange, onBlur, value, ref } }) => (
          // Gắn các hàm sự kiện của `control` trong useForm vào thư viện ngoài
          <ReactDatePicker
            onChange={onChange}
            onBlur={onBlur}
            selected={value}
          />
        )}
      />

      <input type="submit" />
    </form>
  );
}
```

## Mảng với useFieldArray

```tsx
import { useForm, useFieldArray } from "react-hook-form";

export default function App() {
  const { register, control, handleSubmit } = useForm({
    defaultValues: {
      users: [{ name: "" }] // Khởi tạo mảng 1 phần tử
    }
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: "users", // tên mảng trong form
  });

  const onSubmit = (data) => console.log(data);

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      {fields.map((field, index) => (
        <div key={field.id}>
          <input {...register(`users.${index}.name`)} />
          <button type="button" onClick={() => remove(index)}>
            Xóa
          </button>
        </div>
      ))}

      <button type="button" onClick={() => append({ name: "" })}>
        Thêm
      </button>

      <input type="submit" />
    </form>
  );
}
```

```tsx
// Bonus
Theo dõi nhiều giá trị: watch() hoặc getValues()
Reset form: reset()
Theo dõi lỗi: formState.errors
```
