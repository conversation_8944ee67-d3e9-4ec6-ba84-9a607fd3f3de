# Socket.io

- Server: `socket.io`
- Client: `socket.io-Client`

```text
CHÚ Ý
1. Socket Server không lưu trữ bất kỳ sự kiện nào
2. Socket Client không phải lúc nào cũng được kết nối
```

## Server

- `io` là đối tượng quản lý toàn bộ `socket`
- `socket` là đối tượng đang connect (Có nhiều đối tượng, `mỗi tab bật lên là 1 đối tượng độc lập với socket.id khác nhau`)

```text
- socket.on(): Đối tượng cụ thể lắng nghe sự kiện
- socket.emit(): Đối tượng cụ thể gửi sự kiện
- io.on(): Luồng socket Server đang lắng nghe sự kiện
- io.emit(): Luồng socket Server đang gửi sự kiện
```

```js
import express from "express";
import { createServer } from "node:http";
import { Server } from "socket.io";

const app = express(); // Phục vụ tạo router
// Tạo server BE
const server = createServer(app);
// Khởi tạo socket với `Server` từ thư viện `socket.io` truyền Server BE vào
const io = new Server(server);

// Lắng nghe sự kiện connection
io.on("connection", (socket) => {
  // Nơi bắt các sự kiện `socket của đối tượng đang connect`
  socket.on("message", (value) => {
    console.log(value);
  });

  socket.emit("hello", "world");
});

server.listen(3000, () => {
  console.log("server running at http://localhost:3000");
});
```

## Client

```js
import { io } from "socket.io-client";

// Khởi tạo socket tại Client
const socket = io({
  ackTimeout: 10000, // Thời gian chờ từ khi gửi đến khi nhận (Vượt quá thời gian coi như mất gói tin)
  retries: 3, // Cố gắng kết nội lại 3 lần khi mất kết nôi
});

// Mã định danh cho mỗi kết nôi
console.log(socket.id); // Có tể gửi kèm giá trị khi emit để xác định ai đang gửi

socket.on("message", (msg) => {
  console.log(msg);
});

const handleSend = (message) => {
  socket.emit("message", message);
};
```

## Nhược điểm

- Hiệu suất có thể không bằng so với cách tạo và duy trì kết nối như `WebSocket`
- `WebSocket` chỉ yêu cầu `một kết nối duy nhất` cho mỗi trình duyệt hoặc thiết bị
- `Socket.io` (sử dụng AJAX polling) thay đổi kết nối khác nếu kết nối hiện tại không đáp ứng nhu cầu (tạo ra `overhead`)

```text
Nói là có thể không bằng vì Socket.io có cả kết nối bằng WebSocket, nếu kết nối đó tốt nhất thì nó sẽ dùng, nếu không nó sẽ dùng kết nối khác
```

## Xử lý máy Client yếu bị Disconnect liên tục

1. `Giảm tần suất ping`: Thiết lập `ping interval` lớn để giảm áp lực lên máy khách yếu. Giúp giảm thông điệp gửi đến máy khác
2. `Thiết lập timeout`: Tăng thời gian chờ phản hồi từ máy khách `(ping timeout)` để đảm bảo rằng kết nối không bị disconnect quá nhanh khi máy khách gặp trục trặc hoặc độ trễ.
3. `Sử dụng Websocket thay vì socket.io`: Nếu máy khách thực sự yếu và không cần các tính năng mở rộng của socket.io, bạn có thể sử dụng websocket trực tiếp để giảm tải cho máy khách.
4. `Tối ưu hóa mã nguồn của ứng dụng`
5. `Xây dựng reconnecting logic`
6. `Kiểm tra phiên bản mới nhất của socket.io`

```ts
// Thiết lập thời gian giữa các lần gửi ping
io.set("pingInterval", 10000); // Gửi ping mỗi 10 giây

// Thiết lập thời gian chờ đợi phản hồi từ máy khách
io.set("pingTimeout", 10000); // Nếu không nhận được pong sau 10 giây, coi là mất kết nối

// Tuy nhiên ảnh hưởng đến thời gian phatshieenj disconnect để xử lý kịp thời
```
