# Query API

- Sử dụng `useQuery()` với các API `GET`
- `useQuery()` c<PERSON> cơ chế catching data với `queryKey` chứa các phụ thuộc để cập nhập lại data

```ts
// API
const accountApiRequest = {
  me: () => http.get("/accounts/me"),
  getEmployee: (id: number) => http.get(`/accounts/detail/${id}`),
};

// Custom Hook
const useAccountProfile = ({ enabled }) => {
  return useQuery({
    queryKey: ["account-profile", id], // Key catching, id thay đổi sẽ gọi lại
    queryFn: accountApiRequest.me,
    enabled: enabled, // Có gọi API hay không
  });
};

const useGetAccount = ({ id }: { id: number }) => {
  return useQuery({
    queryKey: ["account", id],
    queryFn: () => accountApiRequest.getEmployee(id),
  });
};
```

```tsx
// Sử dụng
const { isPending, isError, data, error } = useAccountProfile();

console.log(data);
```
