# Scrum

- `<PERSON><PERSON>` là một `quy trình` phát triển phần mềm theo phương pháp Agile

## I. 3 giá trị cốt lõi tạo lên Scrum

### Minh bạch

- Thông tin minh bạch giữa các bên: <PERSON><PERSON><PERSON> nh<PERSON>h<PERSON>, y<PERSON><PERSON> c<PERSON><PERSON> hà<PERSON>, tiến độ công việc, ...

### Thanh tra

- Thanh tra liên tục đảm bảo phát hiển ra vấn đề và giải pháp

### Thích nghi

- Dựa vào thông tin minh bạch từ các quá trình thanh tra, Scrum có thể phản hồi các thay đổi một cách tích cực

## II. 3 vai trò trong Scrum

### Product Owner

- Người chịu trách nhiệm về sự thành công củ<PERSON> dự á<PERSON>, ngườ<PERSON> định nghĩa các yêu cầu và đánh giá cuối cùng

### Scrum Master

- Ng<PERSON><PERSON>i có hiểu biết sâu sắc về Scrum và đảm bảo nhóm có thể làm việc hiệu quả với Scrum

### Development Team

- Nhóm tiến hành chuyển đổi các yêu cầu được tổ chức trong Product Backlog thành chức năng của hệ thống.

## III. 4 cuộc họp

Sprint là một phân đoạn lặp đi lặp lại trong quy trình phát triển phần mềm, thường có khung thời gian ngắn (từ 1 – 4 tuần).

### Sprint Planning (Họp Kế hoạch Sprint)

- Dev gặp gỡ với Product Owner để lên kế hoạch làm việc cho một Sprint
- Chọn lựa các yêu cầu cần phải phát triển, phân tích và nhận biết các công việc phải làm kèm theo các ước lượng thời gian cần thiết để hoàn tất các tác vụ

### Daily Scrum (Họp Scrum hằng ngày)

- Chia sẻ tiến độ công việc, chia sẻ khó khăn

### Sprint Review (Họp Sơ kết Sprint)

- Rà soát các công việc hoàn tất trong Sprint vừa qua và đề xuất các chỉnh sửa hoặc thay đổi cần thiết

### Sprint Retrospective (Họp Cải tiến Sprint)

- Rà soát lại toàn diện Sprint vừa kết thúc và tìm cách cải tiến quy trình làm việc cũng như sản phẩm

## IV. Công cụ

### Product backlog

- Danh sách ưu tiên các yêu cầu của dự án

### Sprint backlog

- Bản kế hoạch cho 1 Sprint

### Burndown Chart

- Biểu đồ hiển thị xu hướng của dự án dựa trên lượng thời gian cần thiết còn lại để hoàn tất công việc

## V. Quy trình

- **`Product Owner` tạo ra `Product Backlog`** chứa các yêu cầu của dự án với các hạng mục được sắp theo `thứ tự ưu tiên`.
- **Đội Dev hiện thực hóa các yêu cầu của `Product Owner`** với sự lặp đi lặp lại các giai đoạn từ 1 đến 4 tuần làm việc (gọi là `Sprint`)
- **Đội Dev họp với `Product Owner` để lập kế hoạch cho từng `Sprint`** tạo ra các `Sprint Backlog`
- **Trong suốt quá trình phát triển, nhóm sẽ phải cập nhật Sprint Backlog** và thực hiện công việc họp hằng ngày `(Daily Scrum)` để chia sẻ tiến độ công việc cũng như các vướng mắc.
- **Khi kết thúc Sprint, nhóm tạo ra các gói phần mềm có chức năng hoàn chỉnh**. Buổi họp Sơ kết Sprint `(Sprint Review)` ở cuối `Sprint` sẽ giúp khách hàng thấy được nhóm đã chuyển giao những gì, còn những gì phải làm.
- **Sau khi kết thúc việc đánh giá Sprint, Scrum Master và nhóm cùng tổ chức họp Cải tiến Sprint (Sprint Retrospective)** để tìm kiếm các cải tiến trước khi `Sprint` tiếp theo bắt đầu.

`Các Sprint sẽ được lặp đi lặp lại cho tới khi nào các hạng mục trong Product Backlog đều được hoàn tất`
