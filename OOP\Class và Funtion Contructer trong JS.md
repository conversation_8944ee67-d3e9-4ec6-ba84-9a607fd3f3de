# Class và Function Contructor

## 1. Funtion Contructor (ES5)

- Là 1 hàm tạo `contructor` viết dưới dạng `function`
- <PERSON><PERSON> dụng `this` và `prototype`

```ts
function car(name) {
  this.name = name;

  this.getNameCar = function {
    return this.name;
  };
}

// Tạo methor bên ngoài với prototype
car.prototype.run = function () {
  console.log("123");
};

const vinfast = new car("vinfast");
```

## 2. Class (ES6)

- Tương tự khi viết dưới dạng `Class`

```ts
class car (name) {
  name: sting
  contructor(name) {
    this.name = name
  }

  getNameCar () {
    return this.name
  }
}

const vinfast = new car("vinfast")
```

## 3. `extends`, `supper`, `constructor` trong `Class`

- `super` để gọi hàm tạo của lớp cha để truyền giá trị từ con vào cha, rồ<PERSON> có thể thêm các phương thức khác
- `super` có thể gọi phương thức của lớp cha trong lớp con
- `constructor`: Nơi truyền dữ liệu để khởi tạo Class (Giống như `tham số` truyền vào)

```tsx
// Lớp cha
class Animal {
  name: string;
  constructor(name) {
    this.name = name;
  }

  speak() {
    console.log(`${this.name} makes a noise.`);
  }
}

// Lớp con kế thừa `Animal`
class Dog extends Animal {
  // `constructor`: Đối số truyền vào class để khởi tạo
  constructor(name, breed) {
    // `super`: Gọi constructor của lớp cha với tham số name (Nghĩa là truyền name vào contructor của Animal)
    super(name);
    this.breed = breed;
  }

  speak() {
    console.log(`${this.name} barks.`);
  }
}

const myDog = new Dog("Buddy", "Labrador");
myDog.speak(); // Output: Buddy barks.
```
