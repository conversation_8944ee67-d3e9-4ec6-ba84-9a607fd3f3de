# So sánh MVC và MVVM

## <PERSON><PERSON><PERSON> quan hệ với View, Module

- `Controller`: <PERSON><PERSON> thuộc vào `View` và `<PERSON><PERSON><PERSON>` để quyết định `View` nào sẽ được hiển thị.
- `ViewModel`: <PERSON><PERSON><PERSON> lậ<PERSON> với `View`, ph<PERSON> thuộc `Modlue`

## Phạm vi và trách nhiệm

- `Controller`: <PERSON><PERSON><PERSON><PERSON> lý luồng điều khiển và xử lý logic trực tiếp liên quan đến `yêu cầu người dùng` và `Model`.
- `ViewModel`: Tập trung vào việc cung cấp dữ liệu và thuận tiện cho `View`, không có trách nhiệm trong việc quản lý luồng điều khiển của ứng dụng.
