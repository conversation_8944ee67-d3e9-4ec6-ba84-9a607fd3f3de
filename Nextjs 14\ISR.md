# ISR

- <PERSON><PERSON><PERSON> soát việc `cập nhật lại` các `trang tĩnh` đã được sinh ra. mà không cần cập nhật toàn bộ ứng dụng
- <PERSON><PERSON> dụng `revalidateTag`

```ts
// API
list: () => http.get<DishListResType>("dishes", { next: { tags: ["dishes"] } }), // Thêm key tag để revalidate Data
```

```ts
// Đăng ký tag để trang được tái tạo lại khi có thay đổi
revalidateTag("dishes"); // Trang này sẽ chạy lại khi tag: `dishes` thay đổi
```
