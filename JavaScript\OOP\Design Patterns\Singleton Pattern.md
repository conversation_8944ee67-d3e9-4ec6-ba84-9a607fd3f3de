# Singleton Pattern

- <PERSON><PERSON><PERSON> bảo 1 lớp `chỉ có 1 instance duy nhất`
- Cung cấp 1 `điểm truy cập toàn cục`

```js
class Logger {
  constructor() {
    if (Logger.instance) return Logger.instance;
    Logger.instance = this;
  }

  log(msg) {
    console.log("[LOG]:", msg);
  }
}

const logger = new Logger();
logger.log("Ứng dụng đã khởi động");
}

export default new UserSession();

// Sử dụng
userSession.getUser();
userSession.setUser({ name: "<PERSON>", email: "<EMAIL>" });
```

## Ứng dụng

- Dùng cho `store`, `service`, `logger`, ...
