# Jquery

## Lưu ý khi sử dụng Jquery

```js
// Sử dụng thông thường
$(document).ready(function () {
  // do some stuff here
});

// Sử dụng với ReactJs
useEffect(() => {
  // Thao tác DOM bằng jQuery sau khi component được gắn vào DOM
  $("#my-element").hide();

  // Cleanup khi component bị unmount
  return () => {
    $("#my-element").show();
  };
}, []);
```

## Các module phổ biến

- `Ajax` – xử lý Ajax (get, post)
- `Atributes` – <PERSON><PERSON> lý các thuộc tính của đối tượng HTML
- `Effect` – xử lý hiệu ứng
- `Event` – xử lý sự kiện
- `Form` – xử lý sự kiện liên quan tới form
- `DOM` – xử lý Data Object Model
- `Selector` – xử lý luồng lách giữa các đối tượng HTML

## Các thuật ngữ

- `Selectors`
- `Tag Name` `$ ('p')`
- `Tag ID` - `$ ('#id')`
- `Tag Class` `$ ('.class')`
- `Function` trong jQuery

```js
// Đảm bảo DOM đã sãn sàng
$(document).ready(function () {
  // do some stuff here
});
```

- `Callback` trong jQuery

```js
$("p").click((event) => {
  console.log("clicked: " + event.target);
});
```

- `Closure` hàm bao đóng
- `Proxy Pattern` trong jQuery
- `Phạm vi (Scope)` trong jQuery
- Các `tham số` trong jQuery
- `Context` trong jQuery
