# React Server Components (RSC)

React Server Components cho phép render components trên server, giảm bundle size và cải thiện performance.

## 🎯 **Server vs Client Components**

### Server Components (Mặc định trong App Router)

```jsx
// app/page.js - Server Component
import { db } from '@/lib/db';

// ✅ Chạy trên server
export default async function HomePage() {
  // Có thể gọi database trực tiếp
  const posts = await db.posts.findMany();
  
  return (
    <div>
      <h1>Latest Posts</h1>
      {posts.map(post => (
        <article key={post.id}>
          <h2>{post.title}</h2>
          <p>{post.excerpt}</p>
        </article>
      ))}
    </div>
  );
}
```

### Client Components

```jsx
'use client'; // Directive bắt buộc

import { useState, useEffect } from 'react';

// ✅ Chạy trên client
export default function Counter() {
  const [count, setCount] = useState(0);
  
  // <PERSON><PERSON> thể sử dụng hooks, event handlers
  useEffect(() => {
    console.log('Count changed:', count);
  }, [count]);
  
  return (
    <div>
      <p>Count: {count}</p>
      <button onClick={() => setCount(count + 1)}>
        Increment
      </button>
    </div>
  );
}
```

## 📊 **So sánh Server vs Client Components**

| Tính năng | Server Components | Client Components |
|-----------|-------------------|-------------------|
| **Data Fetching** | ✅ Direct DB access | ❌ API calls only |
| **Bundle Size** | ✅ Zero JS bundle | ❌ Adds to bundle |
| **Hooks** | ❌ Không hỗ trợ | ✅ Đầy đủ |
| **Event Handlers** | ❌ Không hỗ trợ | ✅ Đầy đủ |
| **Browser APIs** | ❌ Không có | ✅ Đầy đủ |
| **State** | ❌ Không có | ✅ useState, etc |
| **Effects** | ❌ Không có | ✅ useEffect, etc |

## 🔄 **Composition Patterns**

### Server Component chứa Client Component

```jsx
// app/page.js - Server Component
import ClientCounter from './ClientCounter';

export default async function Page() {
  const data = await fetchData();
  
  return (
    <div>
      <h1>Server Rendered Content</h1>
      <p>Data: {data.message}</p>
      
      {/* Client component được embed */}
      <ClientCounter initialCount={data.count} />
    </div>
  );
}
```

```jsx
// ClientCounter.js - Client Component
'use client';

import { useState } from 'react';

export default function ClientCounter({ initialCount }) {
  const [count, setCount] = useState(initialCount);
  
  return (
    <div>
      <p>Count: {count}</p>
      <button onClick={() => setCount(count + 1)}>+</button>
    </div>
  );
}
```

### Client Component chứa Server Component (qua children)

```jsx
// app/layout.js - Server Component
import ClientWrapper from './ClientWrapper';
import ServerSidebar from './ServerSidebar';

export default function Layout({ children }) {
  return (
    <html>
      <body>
        <ClientWrapper>
          {/* Server component được pass qua children */}
          <ServerSidebar />
          {children}
        </ClientWrapper>
      </body>
    </html>
  );
}
```

```jsx
// ClientWrapper.js - Client Component
'use client';

import { useState } from 'react';

export default function ClientWrapper({ children }) {
  const [isOpen, setIsOpen] = useState(false);
  
  return (
    <div className={`layout ${isOpen ? 'open' : ''}`}>
      <button onClick={() => setIsOpen(!isOpen)}>
        Toggle Sidebar
      </button>
      
      <div className="content">
        {children} {/* Server components */}
      </div>
    </div>
  );
}
```

## 🗄️ **Data Fetching trong Server Components**

### Database Access

```jsx
// app/users/page.js
import { db } from '@/lib/prisma';

export default async function UsersPage() {
  // Trực tiếp query database
  const users = await db.user.findMany({
    include: {
      posts: true,
      profile: true
    }
  });
  
  return (
    <div>
      <h1>Users ({users.length})</h1>
      {users.map(user => (
        <UserCard key={user.id} user={user} />
      ))}
    </div>
  );
}

// Cũng là Server Component
function UserCard({ user }) {
  return (
    <div className="user-card">
      <h3>{user.name}</h3>
      <p>{user.email}</p>
      <p>Posts: {user.posts.length}</p>
    </div>
  );
}
```

### API Calls với Caching

```jsx
// lib/api.js
export async function getUser(id) {
  const res = await fetch(`https://api.example.com/users/${id}`, {
    next: { revalidate: 3600 } // Cache 1 hour
  });
  
  if (!res.ok) {
    throw new Error('Failed to fetch user');
  }
  
  return res.json();
}

// app/users/[id]/page.js
import { getUser } from '@/lib/api';

export default async function UserPage({ params }) {
  const user = await getUser(params.id);
  
  return (
    <div>
      <h1>{user.name}</h1>
      <p>{user.bio}</p>
    </div>
  );
}
```

### Parallel Data Fetching

```jsx
// app/dashboard/page.js
async function getStats() {
  const res = await fetch('/api/stats');
  return res.json();
}

async function getRecentPosts() {
  const res = await fetch('/api/posts/recent');
  return res.json();
}

async function getUsers() {
  const res = await fetch('/api/users');
  return res.json();
}

export default async function Dashboard() {
  // Fetch parallel
  const [stats, posts, users] = await Promise.all([
    getStats(),
    getRecentPosts(),
    getUsers()
  ]);
  
  return (
    <div className="dashboard">
      <StatsWidget stats={stats} />
      <RecentPosts posts={posts} />
      <UsersList users={users} />
    </div>
  );
}
```

## 🎨 **Streaming với Suspense**

```jsx
// app/dashboard/page.js
import { Suspense } from 'react';

export default function Dashboard() {
  return (
    <div>
      <h1>Dashboard</h1>
      
      {/* Render ngay lập tức */}
      <QuickStats />
      
      {/* Stream khi ready */}
      <Suspense fallback={<PostsSkeleton />}>
        <RecentPosts />
      </Suspense>
      
      <Suspense fallback={<UsersSkeleton />}>
        <UsersList />
      </Suspense>
    </div>
  );
}

// Server Component với slow data
async function RecentPosts() {
  // Slow API call
  await new Promise(resolve => setTimeout(resolve, 2000));
  const posts = await fetchRecentPosts();
  
  return (
    <div>
      <h2>Recent Posts</h2>
      {posts.map(post => (
        <div key={post.id}>{post.title}</div>
      ))}
    </div>
  );
}
```

## 🔧 **Environment Variables**

```jsx
// Server Component - Có thể access tất cả env vars
export default async function ServerPage() {
  // ✅ Có thể dùng private keys
  const data = await fetch('https://api.example.com/data', {
    headers: {
      'Authorization': `Bearer ${process.env.SECRET_API_KEY}`
    }
  });
  
  return <div>{/* render data */}</div>;
}
```

```jsx
// Client Component - Chỉ NEXT_PUBLIC_* vars
'use client';

export default function ClientPage() {
  // ✅ Chỉ public env vars
  const publicKey = process.env.NEXT_PUBLIC_API_KEY;
  
  // ❌ Undefined - không access được private vars
  const secretKey = process.env.SECRET_API_KEY;
  
  return <div>{/* component logic */}</div>;
}
```

## 🚀 **Performance Benefits**

### Bundle Size Comparison

```jsx
// ❌ Traditional Client-side (tất cả gửi về client)
import { format } from 'date-fns'; // +60KB
import { marked } from 'marked';   // +45KB
import hljs from 'highlight.js';   // +200KB

function BlogPost({ post }) {
  const formattedDate = format(new Date(post.date), 'PPP');
  const html = marked(post.content);
  
  return (
    <article>
      <time>{formattedDate}</time>
      <div dangerouslySetInnerHTML={{ __html: html }} />
    </article>
  );
}
```

```jsx
// ✅ Server Component (0KB gửi về client)
import { format } from 'date-fns'; // Chạy trên server
import { marked } from 'marked';   // Chạy trên server

export default async function BlogPost({ params }) {
  const post = await getPost(params.slug);
  
  // Xử lý trên server
  const formattedDate = format(new Date(post.date), 'PPP');
  const html = marked(post.content);
  
  return (
    <article>
      <time>{formattedDate}</time>
      <div dangerouslySetInnerHTML={{ __html: html }} />
    </article>
  );
}
```

## 📋 **Best Practices**

### 1. Khi nào dùng Server Components

```jsx
// ✅ Tốt cho Server Components
- Data fetching
- Static content
- SEO-critical content
- Large dependencies
- Database queries
- File system access

// ✅ Tốt cho Client Components  
- Interactivity (onClick, onChange)
- State management (useState)
- Effects (useEffect)
- Browser APIs
- Custom hooks
```

### 2. Composition Strategy

```jsx
// ✅ Tốt: Server component ở ngoài
export default async function Page() {
  const data = await fetchData();
  
  return (
    <div>
      <ServerContent data={data} />
      <ClientInteractiveWidget />
    </div>
  );
}

// ❌ Tránh: Client component wrap Server component
'use client';
export default function Page() {
  return (
    <div>
      <ServerContent /> {/* Sẽ không hoạt động */}
    </div>
  );
}
```

### 3. Error Handling

```jsx
// app/users/[id]/page.js
import { notFound } from 'next/navigation';

export default async function UserPage({ params }) {
  try {
    const user = await getUser(params.id);
    
    if (!user) {
      notFound(); // Trigger 404 page
    }
    
    return <UserProfile user={user} />;
  } catch (error) {
    throw error; // Trigger error boundary
  }
}
```

## 🚨 **Limitations**

1. **Không thể sử dụng:**
   - React hooks (useState, useEffect, etc.)
   - Event handlers (onClick, onChange, etc.)
   - Browser APIs (localStorage, window, etc.)
   - React context (useContext)

2. **Serialization:**
   - Props phải serializable (JSON)
   - Không thể pass functions, classes, dates

3. **Import restrictions:**
   - Không thể import client-only libraries
   - Cần careful với third-party packages
