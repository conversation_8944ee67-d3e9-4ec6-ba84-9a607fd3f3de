# Tối ưu Performance

## 1. useMemo(), useCallback(), React.memo()

- Đ<PERSON>h giá 1 `function` trong `Component` có nên reRender không khi `Componnet` chứa nó reRender
- `useMemo()` khác `useCallback()` là `function` truyền vào `useMemo()` bắt buộc phải ở trong quá trình `render`
- `useMemo()` và `useCallback()` tốn bộ nhớ

## 2. React.PureComponent

- Sử dụng `shouldComponentUpdate` để xác định sự thay đổi của `State` và `Props` để đánh giá xem có reRender hay không

```js
// Sử dụng bằng cách extends React.PureComponent
class ReactComponent extends React.PureComponent {
  constructor(props, context) {
    super(props, context);
    this.state = {
      data: null,
    };
    this.inputValue = null;
  }

  render() {
    return <div>{this.state.data}</div>;
  }
}
```

## 3. react-virtualized

- Đ<PERSON> render 1 list nhiều item (scroll đến đâu render đến đó như FB)

## 4. React.lazy()

- Chỉ tải Component cần thiết khi cần sử dụng

```tsx
import React, { lazy, Suspense } from "react";

const MyComponent = lazy(() => import("./MyComponent")); // Trả về 1 Promise

function App() {
  return (
    <div>
      // Suspense Chạy khi compoenet chưa tải xong
      <Suspense fallback={<div>Loading...</div>}>
        {" "}
        <MyComponent />
      </Suspense>
    </div>
  );
}
```
