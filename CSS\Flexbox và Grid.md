# Flexbox với Grid

`https://200lab.io/blog/flexbox-vs-grid/`

- <PERSON><PERSON><PERSON> dựng bố cục Website

## Flexbox (content-first)

- `Flexbox` giúp bạn tạo ra layout `1 chiều` (chiề<PERSON> ngang hoặc chiều dọc)
- Thường dùng cho giao diện đơn giản như <PERSON> d<PERSON>, thanh điều hướ<PERSON>, ...

```css
.container {
  display: flex; // Các phần tử con chia đều kích thước
  display: inline-flex; // Các phần tử con chỉ lấy đủ kích thước cần hiển thị
}
```

## Grid (layout-first)

- `Grid` giúp bạn tạo ra layout `2 chiều` (chiều ngang và chiều dọc)
- Thường dùng cho giao diện phức tạp nhiều hàng nhiều cột

```css
.container {
  display: grid;
}
```
