# Static Rendering vs Dynamic Rendering

- Next 14 mặc định fetch là { cache: 'force-cache'} nghĩa là static rendering khi dùng fetch

```text
Chạy npm run build để kiểm tra các page đang thuộc loại nào
```

- Next để mặc định các page là `Static Rendering`
- Trong Next chỉ `Server component` bị ảnh hưởng bởi `Dynamic Rendering`
- Page tự động chuyển sang `Dynamic Rendering` khi ta dùng `Dynamic function` trong component tree (children, parent component, layout, ...): `cookies`, `header`, `searchParams`

```text
Cách xử lý:
- cookies: Không sử dụng nữa, thay vào đó lưu vào `localStorage`
- searchParams: Bọc Component sử dụng `searchParams` bằng thẻ `<Suspense>`
- header:
```

## Static Rendering

- Tạo HTML ngay khi `chạy run build` trong `.next/server/app`
- Tối ưu, giảm tải hiệu năng vì chỉ tạo `1 lần khi build`
- <PERSON><PERSON> hợp nội dung `ít thay đổi`, c<PERSON> thể `caching`

## Dynamic Rendering

- Tạo HTML mỗi khi `request đến Server`
- Không tối ưu vì cứ mỗi lần có `1 request` sẽ tạo HTML. không quan tâm có trung với cái trước đó không
- Phù hợp nội dung `thay đổi thường xuyên`

## Param và Search Param

- `Search Param`: `?a=1&b=2` (query parameters)
- `Param`: `products/1` (dynamic route parameters)
