# window.onload và $("document").ready()

- Khi nào document render thì hàm đó mới được thực thi
- <PERSON><PERSON> có thể gọi hàm trước khi khai báo

## window.onload

- Ch<PERSON>y sau khi trình duyệt đã `load xong mọi thứ` (image, js, css)

```js
window.onload = function () {
  do_validate();
};

function do_validate() {
  alert(1);
}
```

## $(document).ready()

- Nếu bạn muốn một sự kiện jQuery nào đó hoạt động bạn phải gọi nó bên trong hàm $(document).ready()
- Ch<PERSON>y `ngay sau khi DOM được load` và `trước khi toàn bộ trang được load`
