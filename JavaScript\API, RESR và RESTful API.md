# API, RESR, RESTfull API

`https://200lab.io/blog/rest-api-la-gi/`

## I. `API`

- Là một giao diện lập trình ứng dụng giúp `1 ứng dụng hay thành phần` tương tác với `1 ứng dụng hay thành phần khác`
- <PERSON><PERSON>i cách khác: `API` là 1 hàm được tạo ra để tương tác giữa các ứng dụng với nhau (thư viện)
- Nó chỉ gọi và truyền giá trị vào hàm, không quan tâm hàm bên trong xử lý thế nào
- Ví dụ:

```text
- URL: là 1 API giúp Client tương tác với Server
- useState(): là 1 API giúp web tương tác với`ReactJS
```

## II. REST

- Là 1 quy ước rằng buộc khi thiết kế hệ thống mạng
- <PERSON><PERSON> dụng phương thức HTTP để giao tiếp
- cho phép Client tương tác với data trên Server mà không cần quan tâm bên Server xử lý thế nào
- Mỗi một request REST API đều không mang theo trạng thái trước đó
- REST có một số ràng buộc:

`Uniform Interface` (Giao diện thông nhất)

`Stateless` (Không trạng thái): Server không giữ trạng thái trước đó

`Cacheable` (Dữ liệu có thể lưu vào bộ nhớ cache): Các phản hồi từ `Server` phải chỉ định rõ có cache lại hay không

`Client-Server`: tách biệt `Client` với `Server`, dễ dang mở rộng độc lập

`Layered System` (Hệ thống phân lớp): Kiến trúc có thể được phân tầng, cho phép các thành phần trung gian (như proxy, gateway) được thêm vào mà không ảnh hưởng đến `client`.

`Code on Demand` (Code theo yêu cầu): Server có thể cung cấp mã thực thi cho client (như `JavaScript`) để mở rộng chức năng của client.

## III. RESTful API

- Là một giao diện lập trình ứng dụng (API) tuân thủ các ràng buộc và quy ước kiến trúc REST truyền tải qua HTTP method
- Tuân thủ theo các quy tắc dưới có thể được coi là RestFull API
- Có nhiều `Endpoint`

### 1. Sử dụng Methods: Phương thức HTTP

- GET: Trả về một Resource hoặc một danh sách Resource.
- POST: Tạo mới một Resource.
- PUT: Cập nhật thông tin cho Resource (toàn bộ resource).
- PATCH: Cật nhật thông tin cho resourse (một phần resource).
- DELETE: Xoá một Resource.

### 2. Cung cấp tài nguyên hợp lý

- Sử dụng `id` định danh cho URL thay vì dùng `query-string`
- Sử dụng `URL query-string` cho việc filter chứ không phải cho việc lấy một tài nguyên

```texy
Good: /users/123
Poor: /api?type=user&id=23
```

- Thiết kế cho người sử dụng chứ không phải thiết kế cho data của bạn
- Giữ cho URL ngắn và dễ đọc nhất cho client
- Sử dụng số nhiều trong URL để có tính nhất quán

### 3. Sử dụng Status code

- `200` OK – Trả về thành công cho những phương thức GET, PUT, PATCH hoặc DELETE.
- `201` Created – Trả về khi một Resouce vừa được tạo thành công.
- `204` No Content – Trả về khi Resource xoá thành công.
- `304` Not Modified – Client có thể sử dụng dữ liệu cache.
- `400` Bad Request – Request không hợp lệ
- `401` Unauthorized – Request cần có auth.
- `403` Forbidden – bị từ chối không cho phép.
- `404` Not Found – Không tìm thấy resource từ URI
- `405` Method Not Allowed – Phương thức không cho phép với user hiện tại.
- `410` Gone – Resource không còn tồn tại, Version cũ đã không còn hỗ trợ.
- `415` Unsupported Media Type – Không hỗ trợ kiểu Resource này.
- `422` Unprocessable Entity – Dữ liệu không được xác thực
- `429` Too Many Requests – Request bị từ chối do bị giới hạn

### 4. Sử dụng định dạng JSON hoặc XML để giao tiếp client-server

- JSON là kiểu dữ liệu tiện dụng cho server và client giao tiếp với nhau.
- Có thể xử dụng XML nhưng phổ biến hơn cả vẫn là JSON

## IV. Kinh nghiệm thêm

### 1. Thiết kế REST API URI

```text
POST /v1/posts (tạo mới một bài viết)
GET /v1/posts (lấy danh sách bài viết)
GET /v1/posts/:post_id (lấy chi tiết bài viết với post_id cụ thể)
PUT /v1/posts/:post_id (update bài viết với post_id cụ thể)
DELETE /posts/:post_id (delete bài viết với post_id cụ thể)
```

## 2. Authentication và dữ liệu trả về

- `RESTful API` không sử dụng `session` và `cookie`, nó sử dụng một `access_token` với mỗi request thông qua `header`. Dữ liệu trả về thường có cấu trúc như sau:

```json
{
  "status_code": 200,
  "data": [
    {
      "name": "ManhLD"
    },
    {
      "name": "Ahri"
    }
  ],
  "error_messages": ""
}
```
