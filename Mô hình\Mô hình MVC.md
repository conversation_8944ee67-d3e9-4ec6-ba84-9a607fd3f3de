# MVC

- Tách biệt logic ứng dụng khỏi giao diện
- Thường sử dụng cho `Web`
- Không hỗ trợ `data binding`. `Controller` xử lý các yêu cầu từ người dùng và quyết định hành động tiếp theo

## Model

- `Chức năng`: <PERSON>uản lý dữ liệu và logic nghiệp vụ của ứng dụng.
- `Tương tác`: Tương tác trực tiếp với cơ sở dữ liệu hoặc các dịch vụ ngoại vi.
- Không có bất kỳ thông tin gì về `View` hay `Controller`

## View

- `Chức năng`: Hiển thị dữ liệu từ `Module` mà `Controller` cung cấp .
- `Tương tác`: Nhận dữ liệu từ `Controller` và hiển thị cho người dùng.
- <PERSON><PERSON> thuộc: `Controller`

## Controller

- `<PERSON><PERSON><PERSON> năng`: <PERSON><PERSON><PERSON><PERSON> khiển luồng dữ liệu giữa `Model` và `View`.
- `Tương tác`: <PERSON><PERSON> lý yêu cầu từ `View`, tương tác với `Model` để lấy và cập nhật dữ liệu, sau đó cập nhật `View` để hiển thị.

## Luồng hoạt động

- `View` gửi các sự kiện người dùng đến `Controller`.
- `Controller` xử lý sự kiện và tương tác với `Model` để cập nhật dữ liệu.
- `Model` thông báo thay đổi cho `View`, và `View` sẽ cập nhật giao diện người dùng.

## DEMO ReactJS MVC

- `Module`: State (Redux, Context API), API Services
- `View`: Component render JSX
- `Controller`: Các hàm `Handler` `tích hợp` trong `Component View`

```tsx
// Module
export const fetchData = async () => {
  const response = await fetch("https://api.example.com/data");
  const data = await response.json();
  return data;
};
```

```tsx
// View
const MyComponent = () => {
  const [data, setData] = useState([]);

  // Controller
  const handleButtonClick = () => {
    const data = fetchData(); // Tương tác Module
    setState(data);
  };

  return (
    <>
      <button onClick={handleButtonClick}>Click</button>
      <div>
        {data.map((item) => (
          <div key={item.id}>{item.name}</div>
        ))}
      </div>
    </>
  );
};
```
