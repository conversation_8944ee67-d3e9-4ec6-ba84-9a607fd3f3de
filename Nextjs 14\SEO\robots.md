# robots.txt

`https://nextjs.org/docs/app/api-reference/file-conventions/metadata/robots#static-robotstxt`

- <PERSON><PERSON><PERSON> cho công cụ tìm kiếm biết nên truy cập và không nên truy cập vào file nào

## Sử dụng trong Next

```ts
// file robots.ts trong `/app`
import { MetadataRoute } from "next";

export default function robots(): MetadataRoute.Robots {
  return {
    rules: {
      userAgent: "*",
      allow: "/", // Cho phép truy cập đường dẫn
      disallow: "/private/", // Không cho truy cập đường dẫn
    },
    sitemap: `https://acme.com/sitemap.xml`,
  };
}
```

## File cuối cùng được Gennerate ra cho công cụ tìm kiếm đọc

```txt
User-Agent: *
Allow: /
Disallow: /private/

Sitemap: https://acme.com/sitemap.xml
```
