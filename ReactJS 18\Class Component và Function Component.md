# Class Component và Function Component

## 1. Class Component

- `Class Component` đư<PERSON>c viết bằng cú pháp của `ES6 class`.
- <PERSON><PERSON><PERSON> cầu phải kế thừa từ lớp `React.Component`.
- <PERSON><PERSON> dụng phương thức `render()` để trả về các phần tử `JSX`.
- <PERSON><PERSON> thể sử dụng các tính năng bổ sung như `state` và `lifecycle methods`.
- Thường được sử dụng cho các thành phần có trạng thái phức tạp

```js
class myClassComponent extends Component {
  render() {
    return <div>Hello</div>;
  }
}
```

## 2. Function Component

- `Function Component` được viết bằng cú pháp hàm thông thường của `JavaScript`.
- <PERSON><PERSON> một hàm JavaScript thông thường, `không có this`.
- <PERSON><PERSON><PERSON> về các phần tử `JSX` ngay từ bên trong hàm.
- Không có state mặc định, nhưng có thể sử dụng `useState` từ `React Hooks`
- Thường được sử dụng cho các thành phần đơn giản, không có nhiều logic.

```js
function myFunctionComponent() {
  return <div>Hello</div>;
}
```
