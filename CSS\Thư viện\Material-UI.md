# Material-UI

- <PERSON><PERSON> cấp giao diện có sẵn (button, card, ...)

## Tùy chỉnh

- <PERSON><PERSON> dụng `createTheme` và `ThemeProvider` để cấu hình tùy chỉnh

```js
// theme.js

import { createTheme } from '@material-ui/core/styles';

// Tạo theme tùy chỉnh
const theme = createTheme({
  palette: {
    primary: {
      main: '#1e40af', // M<PERSON>u ch<PERSON>
    },
    success: {
      main: '#10b981', // Màu thành công
    },
  },
});
export default theme;

// App.js
import React from 'react';
import { ThemeProvider } from '@material-ui/core/styles';
import theme from './theme';
import App from './App';

function Root() {
  return (
    <ThemeProvider theme={theme}>
      <App />
    </ThemeProvider>
  );
}

export default Root;
```
