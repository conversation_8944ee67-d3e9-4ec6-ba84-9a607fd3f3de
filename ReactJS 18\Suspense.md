# Suspense for Data Fetching

- <PERSON><PERSON>n render UI cho đến khi có UI hoàn chỉnh

## 🎯 **Suspense Cơ bản**

### Code Splitting (React 16+)

```jsx
import { Suspense, lazy } from 'react';

const LazyComponent = lazy(() => import('./LazyComponent'));

function App() {
  return (
    <Suspense fallback={<div>Loading component...</div>}>
      <LazyComponent />
    </Suspense>
  );
}
```

### Data Fetching (React 18)

```jsx
import { Suspense } from 'react';

// Giả sử có data fetching library hỗ trợ Suspense
function UserProfile({ userId }) {
  const user = useUser(userId); // Throws promise nếu chưa load xong
  
  return (
    <div>
      <h1>{user.name}</h1>
      <p>{user.email}</p>
    </div>
  );
}

function App() {
  return (
    <Suspense fallback={<div>Loading user...</div>}>
      <UserProfile userId={1} />
    </Suspense>
  );
}
```

## 🚨 **Lưu ý quan trọng**

1. **Suspense chỉ hoạt động với:**
   - React.lazy()
   - Libraries hỗ trợ Suspense (React Query, SWR, Relay)
   - Custom implementations `throw promises`

2. **Không hoạt động với:**
   - useEffect + useState
   - `Async/await` trong component body
   - `Promise.then()` trong render

3. **Server-side rendering:**
   - Cần framework hỗ trợ (Next.js, Remix)
   - Streaming SSR cần setup đặc biệt
