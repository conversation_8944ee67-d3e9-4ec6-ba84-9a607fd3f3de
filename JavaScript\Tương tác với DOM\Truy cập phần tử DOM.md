# Truy cập phần tử DOM

## document.getElementById()

```tsx
<div id="demo">Access me by ID</div>;

const demoId = document.getElementById("demo");
demoId.style.border = "1px solid purple";

// Output demoId: <div id="demo">Access me by ID</div>
```

## document.getElementsByClassName()

```tsx
<div class="demo">Access me by class</div>;
<div class="demo">Access me by class</div>;

const demoClass = document.getElementsByClassName("demo");

// Output demoClass: [div.demo, div.demo]
```

## document.getElementsByTagName()

```tsx
<article>Access me by tag (1)</article>
<article>Access me by tag (2)</article>

const demoClass = document.getElementsByTagName("article");
for (i = 0; i < demoTag.length; i++) {
  demoTag[i].style.border = '1px solid blue';
}
```

## document.querySelector() và querySelectorAll()

- ID: `#`
- class: `.`

```tsx
<div class="demo-query-all">Access me by query all (1)</div>
<div class="demo-query-all">Access me by query all (2)</div>

const demoQueryAll = document.querySelectorAll("article");
demoQueryAll.forEach((query) => {
  query.style.border = "1px solid green";
});
```
