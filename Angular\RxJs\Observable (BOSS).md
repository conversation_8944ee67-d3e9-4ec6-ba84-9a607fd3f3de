# Observable (Chỉ phát giá trị)

- `Observable` là một lớp `cốt lõi` đ<PERSON><PERSON><PERSON> sử dụng để biểu diễn các `luồng dữ liệu không đồng bộ`
- <PERSON><PERSON> thể `phát` ra nhiều giá trị theo thời gian và được `lắng nghe` (`subscribed`) bởi các `Observer`

## Khởi tạo

- Sử dụng `new Observable()`
- Sử dụng `operators`: `of`, `from`, `interval`, v.v.

## Ví dụ

- Sử dụng `new Observable()`

```ts
import { Observable } from "rxjs";

// Tạo một Observable đơn giản (Bắn 2 dữ liệu sau đó bắn hoàn thành)
const observable = new Observable((subscriber) => {
  subscriber.next("Hello");
  subscriber.next("World");
  subscriber.complete();
});

// <PERSON><PERSON><PERSON> ký (subscribe) để nhận dữ liệu từ Observable
observable.subscribe({
  next(x) {
    console.log("Received value: " + x);
  },
  error(err) {
    console.error("Something went wrong: " + err);
  },
  complete() {
    console.log("Done");
  },
});
```

- Sử dụng `operators`

```ts
import { of } from "rxjs";
import { map, filter } from "rxjs/operators";

// of: phát ra các giá trị theo thứ tự
const numbers$ = of(1, 2, 3, 4, 5);

// pipe: kết hợp `filter` và `map`
const evenSquares$ = numbers$.pipe(
  filter((n) => n % 2 === 0), // filter: chỉ cho phép đi qua theo điều kiện
  map((n) => n * n) // map: xử lý, tính toán
);

evenSquares$.subscribe({
  next(x) {
    console.log("Received value: " + x);
  },
  error(err) {
    console.error("Something went wrong: " + err);
  },
  complete() {
    console.log("Done");
  },
});
```
