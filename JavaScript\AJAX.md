# `Ajax`

- <PERSON><PERSON> một kỹ thuật lập trình web cho phép gửi và nhận dữ liệu giữa trang web và máy chủ mà không cần tải lại trang
- Là tập hợp các công nghệ:

```text
- HTML/XHTML: làm ngôn ngữ chính và CSS.
- DOM: Hiển thị dữ liệu động và tạo tương tác.
- XML: Trao đổi dự liệu nội bộ và XSLT để xử lý nó. Nhiều lập trình viên đã thay thế bằng JSON vì nó gần với JavaScript hơn.
- XMLHttpRequest: Giao tiếp bất đồng bộ.
- JavaScript: làm ngôn ngữ lập trình để kết nối toàn bộ các công nghệ trên lại.
```

## 1. Luồng web sử dụng Ajax

- Người dùng tương tác với trang web, và kích hoạt sự kiện bằng cách nhấn nút, vv.
- Mã `JavaScript` được thực thi gửi yêu cầu đến máy chủ thông qua đối tượng `XMLHttpRequest` (XHR).
- Máy chủ xử lý yêu cầu và gửi lại dữ liệu đến máy khách dưới dạng `XML`, `JSON` hoặc `văn bản thuần`.
- Dữ liệu được xử lý bởi `JavaScript` và thêm vào trang web một cách động mà không cần tải lại trang.

## 2. Luồng web thông thường

- Người dùng tương tác với trang web.
- Trình duyệt gửi yêu cầu đến máy chủ.
- Máy chủ xử lý yêu cầu và tạo ra `một trang web hoàn toàn mới`.
- Máy chủ gửi trang web hoàn toàn mới cho trình duyệt.
- Trình duyệt hiển thị trang web mới cho người dùng.
