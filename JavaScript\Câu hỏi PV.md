# Câu hỏi PV

## instanceof

- Ki<PERSON>m tra xem một đối tượng (`object`) có phải là một thể hiện của một hàm tạo (constructor) không

```js
function Car(make, model) {
  this.make = make;
  this.model = model;
}

const myCar = new Car("Toyota", "Corolla");

console.log(myCar instanceof Car); // true
```

## for...of và for...in

- `for...of`: Sử dụng cho `mảng`

```ts
const numbers = [];
for (let number of numbers) {
  console.log(number);
}
```

- `for...in`: Sử dụng cho `object`

```ts
const person = {};

for (let key in person) {
  console.log(key + ": " + person[key]);
}
```

## dangerouslySetInnerHTML

- Render chuỗi HTML vào DOM
- Dễ tạo lỗ hổng bảo mật XSS

```js
const data = "<div><p>HTML từ backend</p></div>";

return <div dangerouslySetInnerHTML={{ __html: data }} />;
```

## Import default và Import name

- Import default

```text
- Mỗi module chỉ có 1 default export
- Có thể đặt tên tùy ý
- Sử dụng khi module chỉ có 1 giá trị cần export
- import anyName from 'module';
```

- Import name

```text
- Sử dụng để import một hoặc nhiều giá trị được export có tên từ một module
- Phải sử dụng đúng tên
- Sử dụng khi module chỉ có nhiều giá trị cần export
- import { namedExport } from 'module';
```
