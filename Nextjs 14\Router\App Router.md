# App Router

`App Router` Sử dụng thư mục `/app`. Nó hỗ trợ React Server Components, nested layout, streaming, và hiện đại hóa toàn bộ cấu trúc ứng dụng.

---

## 1. <PERSON><PERSON><PERSON><PERSON>c App Router

```tsx
/app
├── layout.js     --> Layout dùng chung
├── page.js       --> Route: "/"
├── about
│ └── page.js     --> Route: "/about"
├── dashboard
│ ├── layout.js   --> Nested layout
│ └── page.js     --> Route: "/dashboard"
├── error.js      --> Error boundary
├── loading.js    --> Loading fallback
└── not-found.js  --> 404 page
```

## 2. Get thông tin URL

- `Server component`: l<PERSON>y từ props `params` , `searchParams`

```tsx
export default function ProductsPage({ params , searchParams }) {
  return <div>Page: {searchParams.page}, {params.page}</div>;
}
```

- `Client component`: sử dụng `useSearchParams()` và `useParams()`

```tsx
export default function ProductsPage() {
  const searchParams = useSearchParams();
  const params = useParams();

  return <div>Page</div>;
}
```

## 3. Điều hướng

- Sử dụng `router.push()` hoặc <Link>

```tsx
import { useRouter } from 'next/navigation'

const router = useRouter()
router.push('/dashboard')
```

| API                    | Tác dụng                                                 |
| ---------------------- | -------------------------------------------------------- |
| `router.push(path)`    | Điều hướng tới `path`                                    |
| `router.replace(path)` | Điều hướng mà không lưu vào history                      |
| `router.back()`        | Quay lại trang trước đó                                  |
| `router.refresh()`     | Refresh lại route hiện tại (hữu ích sau khi mutate data) |


## 🚦 4. Data Fetching

- Chỉ sử dụng bên `Serve component`

|                         | **Pages Router (`/pages`)**                      | **App Router (`/app`)**                 |
| ----------------------- | ------------------------------------------------ | --------------------------------------- |
| Server rendering (SSR)  | `getServerSideProps()`                           | ✅ Dùng `fetch()` + cache: "no-store"             |
| Static generation (SSG) | `getStaticProps()`                               | ✅ Dùng `fetch()`, nếu + `revalidate` => (ISR)          |
| Dynamic routing         | `getStaticPaths()`                               | ✅ `generateStaticParams()`              |

- Ví dụ: `generateStaticParams()` dùng cho router động để lấy danh sách tất cả router động cần build HTML

```tsx
// app/blog/[slug]/page.tsx
export async function generateStaticParams() {
  const posts = await fetch("https://api.com/posts").then(res => res.json());

  return posts.map((post: any) => ({
    slug: post.slug,
  }));
}

export default async function BlogDetailPage({
  params,
}: {
  params: { slug: string }; // nhận tất cả posts.slug (trên generateStaticParams()) tại đây để tạo HTML trên Serve
}) {
  const post = await getPostBySlug(params.slug);

  return (
    <main className="max-w-2xl mx-auto p-6">
      <h1 className="text-2xl font-bold mb-4">{post.title}</h1>
      <p>{post.content}</p>
    </main>
  );
}
```

## 📡 5. API Routes

- Được đặt trong `/pages/api`, mỗi file tương ứng với một endpoint.

```tsx
// app/api/hello/route.ts
export async function GET() {
  return Response.json({ message: 'Hello from App Router!' })
}
```

## 🎨 6. Head, Metadata

```tsx
// app/about/page.js
export const metadata = {
  title: 'About Page',
  description: 'Trang giới thiệu về chúng tôi',
}
```

## 🎨 7. Middleware

- Đặt middleware.js ở thư mục gốc để can thiệp request:

```tsx
// middleware.js
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  const token = request.cookies.get('token')?.value

  if (!token && request.nextUrl.pathname.startsWith('/protected')) {
    return NextResponse.redirect(new URL('/login', request.url))
  }

  return NextResponse.next()
}
```