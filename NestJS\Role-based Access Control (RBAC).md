# Role-based Access Control (RBAC)


## 1. Decorator @Roles()

- <PERSON><PERSON> tái sử dụng dễ dang
- <PERSON><PERSON> thể không cần, khi đ<PERSON> cần viết logic trong `CanActivate`

```ts
// roles.decorator.ts
import { SetMetadata } from '@nestjs/common';
export const ROLES_KEY = 'roles';
export const Roles = (...roles: Role[]) => SetMetadata(ROLES_KEY, roles);
```

## 2. Guard kiểm tra quyền

```ts
// roles.guard.ts
import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { ROLES_KEY } from './roles.decorator';
import { Role } from './roles.enum';

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    // context: dữ liệu trong request g<PERSON><PERSON> lên
    const requiredRoles = this.reflector.getAllAndOverride<Role[]>(
      ROLES_KEY,
      [context.getHandler(), context.getClass()]
    );
    if (!requiredRoles) return true;

    const { user } = context.switchToHttp().getRequest();
    return requiredRoles.includes(user.role);
  }
}

```

## 3. Gắn Guard và Roles vào route

```ts
// app.controller.ts
import { Controller, Get, UseGuards } from '@nestjs/common';
import { Roles } from './roles.decorator';
import { Role } from './roles.enum';
import { RolesGuard } from './roles.guard';
import { AuthGuard } from '@nestjs/passport'; // dùng JWT

@UseGuards(AuthGuard('jwt'), RolesGuard)
@Controller()
export class AppController {
  @Get('admin')
  @Roles(Role.Admin)
  getAdmin() {
    return 'Admin content';
  }
}
```

## Tóm tắt chuỗi gọi hàm:
1. Route hit
2. `AuthGuard('jwt')` chạy
    - Gọi `JwtStrategy.validate()` để decode token
    - Nếu thành công → gán `req.user`
3. `RolesGuard.canActivate()` chạy   
    - Lấy `@Roles()` của handler
    - So sánh `user.role` với roles yêu cầu
4. ✅ Nếu hợp lệ → gọi controller method `getEditor()`
5. ❌ Nếu không hợp lệ → trả về 403 Forbidden

