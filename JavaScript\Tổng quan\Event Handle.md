# Event Handler

- <PERSON><PERSON><PERSON> và sử dụng để truyền dữ liệu

```js
// Tạo Event với tên 'myEvent'
const customEvent = new CustomEvent("myEvent", {
  detail: {
    message: "Hello from custom event!",
  },
});

// <PERSON><PERSON><PERSON> ho<PERSON> sự kiện
document.dispatchEvent(customEvent);

// Lắng <PERSON>he Sự Kiện (Đặt tại nơi cần nhận sự kiện)
document.addEventListener("myEvent", function (event) {
  console.log(event.detail.message);
```

## Luồng

- Dữ liệu và sự kiện được khai báo trước đó
- Sau khi gọi document.dispatchEvent(), sự kiện đó sẽ được bắn đi
- Nơi lắng nghe sự kiện đó sẽ nhận được dữ liệu
