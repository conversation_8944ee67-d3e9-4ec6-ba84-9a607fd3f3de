# Tạo file `ecosystem.config.js` cho `pm2`

```bash
# Start ứng dụng với pm2
pm2 start ecosystem.config.js
# Hoặc dùng thêm env
pm2 start ecosystem.config.js --env production
```

- <PERSON><PERSON> dụ cho dự án Next

```js
module.exports = {
  apps: [
    {
      name: "my-app",
      script: "./node_modules/next/dist/bin/next",
    },
  ],
};
```

- Nếu dùng cho dự án ReactJS thì sửa như sau

```js
// ecosystem.config.js
module.exports = {
  apps: [
    {
      name: "my-app",
      script: "node dist/index.js",
      env: {
        NODE_ENV: "development", // Riêng NODE_ENV thì có thể dùng process.env.NODE_ENV hoặc process.NODE_ENV, còn lại thì chỉ được dùng process.env.TEN_BIEN
        TEN_BIEN: "Gia tri",
      },
      env_production: {
        NODE_ENV: "production",
      },
    },
  ],
};
```
