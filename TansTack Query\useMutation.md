# Post API

- Sử dụng `useMutation()` và `mutateAsync()` với các API `POST`, `PUT`, `DELETE`

```tsx
const authApiRequest = {
  login: (body) => http.post("api/auth/login", body),
  addEmployee: (body) => http.post(`/accounts`, body),
};

const useLoginMutation = () => {
  return useMutation({
    mutationFn: authApiRequest.login,
  });
};

const useAddAccountMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: accountApiRequest.addEmployee,
    // Cập nhập lại khi thành công
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["accounts"], // Get lại data useQuery có key trùng
      });
    },
  });
};
```

```tsx
// Sử dụng
const loginMutation = useLoginMutation();

const handleLogin = async (data) => {
  if (loginMutation.isPending) return;
  await loginMutation.mutateAsync(data);
};
```
