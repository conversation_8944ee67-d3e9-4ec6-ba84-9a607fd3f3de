# JavaScript là đơn luồng (Single-thread)

- Trong 1 thời điểm chỉ thực hiện 1 công việc

## Call Stack và Even Loop

`https://viblo.asia/p/javascript-event-loop-va-call-stack-djeZ1zvYlWz`

1. <PERSON><PERSON><PERSON> hàm thực thi được gọi sẽ được đưa vào `Call Stack` `(FILO)`
2. Mỗi hàm chạy xong sẽ bị loại bỏ khỏi `Call Stack`
3. Khi có hàm bất đông bộ, nó sẽ được đưa vào` Web APIs` để thực thi và loại bỏ khỏi `Call Stack`
4. <PERSON>hi hàm bất đông bộ thực thi xong thì sẽ đưa vào `CallBack Queuse` `(FIFO)`
5. <PERSON>u <PERSON>hi `Call Stack` không còn tác vụ nào cần thực thi. Th<PERSON> hàm trong `CallBack Queuse` sẽ được đẩy vào `Call Stack` thông qua `Event Loop`

![alt text](image.png)

## Macrotask và Microtask 
- Microtask được ưu tiên xử lý trước Macrotask 

| 🧠 **Microtask**                                | ⏰ **Macrotask**                             |
| ----------------------------------------------- | ------------------------------------------- |
| `Promise.then()`                                | `setTimeout()`                              |
| `Promise.catch()`                               | `setInterval()`                             |
| `Promise.finally()`                             | `setImmediate()`                            |
| `queueMicrotask()`                              | `requestAnimationFrame()`                   |
| `MutationObserver`                              | `fetch()` (callback `.then()` là microtask) |
| `process.nextTick()` (Node.js – siêu microtask) | `XMLHttpRequest` callback                   |
|                                                 | `DOM Events` (click, input, etc.)           |


