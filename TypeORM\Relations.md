# Relations trong TypeORM

## 1. OneToMany & ManyToOne

### User c<PERSON>u Posts
```ts
// user.entity.ts
@Entity()
export class User {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @OneToMany(() => Post, post => post.user)
  posts: Post[];
}

// post.entity.ts
@Entity()
export class Post {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  title: string;

  @ManyToOne(() => User, user => user.posts)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column()
  user_id: number;
}
```

### Sử dụng Relations
```ts
// Lấy user với posts
const userWithPosts = await userRepo.findOne({
  where: { id: 1 },
  relations: ['posts']
});

// Hoặc dùng QueryBuilder
const userWithPosts = await userRepo
  .createQueryBuilder('user')
  .leftJoinAndSelect('user.posts', 'post')
  .where('user.id = :id', { id: 1 })
  .getOne();
```

## 2. OneToOne

### User có một Profile
```ts
// user.entity.ts
@Entity()
export class User {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @OneToOne(() => Profile, profile => profile.user)
  profile: Profile;
}

// profile.entity.ts
@Entity()
export class Profile {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  bio: string;

  @OneToOne(() => User, user => user.profile)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column()
  user_id: number;
}
```

## 3. ManyToMany

### User có nhiều Roles
```ts
// user.entity.ts
@Entity()
export class User {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @ManyToMany(() => Role, role => role.users)
  @JoinTable({
    name: 'user_roles',
    joinColumn: { name: 'user_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'role_id', referencedColumnName: 'id' }
  })
  roles: Role[];
}

// role.entity.ts
@Entity()
export class Role {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @ManyToMany(() => User, user => user.roles)
  users: User[];
}
```

### Thao tác ManyToMany
```ts
// Thêm role cho user
const user = await userRepo.findOne({ 
  where: { id: 1 }, 
  relations: ['roles'] 
});
const role = await roleRepo.findOne({ where: { id: 1 } });

user.roles.push(role);
await userRepo.save(user);

// Xóa role khỏi user
user.roles = user.roles.filter(r => r.id !== role.id);
await userRepo.save(user);
```

## 4. Eager & Lazy Loading

### Eager Loading
```ts
@Entity()
export class User {
  @OneToMany(() => Post, post => post.user, { eager: true })
  posts: Post[]; // Tự động load posts khi load user
}

// Sử dụng
const user = await userRepo.findOne({ where: { id: 1 } });
// user.posts đã được load sẵn
```

### Lazy Loading
```ts
@Entity()
export class User {
  @OneToMany(() => Post, post => post.user, { lazy: true })
  posts: Promise<Post[]>; // Load khi cần
}

// Sử dụng
const user = await userRepo.findOne({ where: { id: 1 } });
const posts = await user.posts; // Load posts khi cần
```

## 5. Cascade Operations

### Cascade Options
```ts
@Entity()
export class User {
  @OneToMany(() => Post, post => post.user, {
    cascade: ['insert', 'update', 'remove']
    // hoặc cascade: true (all operations)
  })
  posts: Post[];
}

// Sử dụng
const user = new User();
user.name = 'John';
user.posts = [
  { title: 'Post 1' },
  { title: 'Post 2' }
];

await userRepo.save(user); // Tự động save posts
```

### Cascade Types
| Type | Mô tả |
|------|-------|
| `insert` | Tự động insert related entities |
| `update` | Tự động update related entities |
| `remove` | Tự động remove related entities |
| `soft-remove` | Tự động soft remove related entities |
| `recover` | Tự động recover related entities |

## 6. Orphaned Row Removal

```ts
@Entity()
export class User {
  @OneToMany(() => Post, post => post.user, {
    orphanedRowAction: 'delete' // Xóa posts không có user
  })
  posts: Post[];
}
```

## 7. Self-Referencing Relations

### Category Tree
```ts
@Entity()
export class Category {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @ManyToOne(() => Category, category => category.children)
  @JoinColumn({ name: 'parent_id' })
  parent: Category;

  @OneToMany(() => Category, category => category.parent)
  children: Category[];
}
```

## 8. Best Practices

### Performance
- ✅ Sử dụng `select` để chỉ lấy fields cần thiết
- ✅ Tránh N+1 queries với `leftJoinAndSelect`
- ✅ Sử dụng pagination cho large datasets
- ✅ Index foreign keys

### Code Organization
- ✅ Định nghĩa relations ở cả 2 phía
- ✅ Sử dụng cascade cẩn thận
- ✅ Lazy loading cho relations lớn
- ✅ Eager loading cho relations nhỏ và thường dùng

### Examples
```ts
// ❌ N+1 Query Problem
const users = await userRepo.find();
for (const user of users) {
  const posts = await postRepo.find({ where: { user_id: user.id } });
}

// ✅ Efficient Query
const users = await userRepo.find({ relations: ['posts'] });

// ✅ With QueryBuilder
const users = await userRepo
  .createQueryBuilder('user')
  .leftJoinAndSelect('user.posts', 'post')
  .getMany();
```
