# TypeORM

```ts
// user.entity.ts
import { Entity, PrimaryGeneratedColumn, Column } from "typeorm";

@Entity()
export class User {
  @PrimaryGeneratedColumn() // ID tự động
  id: number;

  @Column()
  name: string;

  @Column({ default: true })
  isActive: boolean;
}
```

## 1. Config để sử dụng trong Module

```ts
// user.module.ts
@Module({
  imports: [TypeOrmModule.forFeature([User])], // 💡 cần thiết để inject Repository
  controllers: [UserController],
  providers: [UserService],
})
export class UserModule {}
```

## 2. Sử dụng trong Service

```ts
// user.service.ts
@Injectable()
export class UserService {
  constructor(
    @InjectRepository(User) // inject Entity
    private userRepository: Repository<User>
  ) {}

  findAll(): Promise<User[]> {
    return this.userRepository.find();
  }

  create(user: Partial<User>): Promise<User> {
    return this.userRepository.save(user);
  }
}
```

## 3. <PERSON><PERSON><PERSON><PERSON> thức phổ biến

| <PERSON><PERSON><PERSON>ng thức        | M<PERSON> t<PERSON>                                                       |
| ------------------ | ----------------------------------------------------------- |
| `find()`           | Lấy tất cả bản ghi                                          |
| `findOne(options)` | Tìm 1 bản ghi với điều kiện                                 |
| `findBy()`         | Tìm nhiều bản ghi với điều kiện                             |
| `findOneBy()`      | Tìm 1 bản ghi với điều kiện                                 |
| `save(entity)`     | Thêm hoặc cập nhật bản ghi                                  |
| `insert(entity)`   | Thêm bản ghi (nhanh hơn `save`, nhưng không trả lại entity) |
| `update(id, data)` | Cập nhật nhanh (không load entity trước)                    |
| `delete(id)`       | Xoá bản ghi                                                 |
| `remove(entity)`   | Xoá entity sau khi load từ DB                               |
| `count()`          | Đếm số lượng bản ghi                                        |
| `exists()`         | Kiểm tra tồn tại (TypeORM 0.3+)                             |

## 4. Truy vấn nâng cao

| Phương thức                                     | Mô tả                                              |
| ----------------------------------------------- | -------------------------------------------------- |
| `find({ where, relations, order, take, skip })` | Truy vấn có điều kiện, phân trang                  |
| `create()`                                      | Tạo instance mới (chưa lưu vào DB)                 |
| `preload()`                                     | Tìm entity và gộp dữ liệu mới (để update)          |
| `merge()`                                       | Gộp dữ liệu vào entity                             |
| `softDelete()`                                  | Soft delete bản ghi (cần bật soft-delete ở entity) |
| `restore()`                                     | Khôi phục bản ghi đã soft-delete                   |

## 5. QueryBuilder

```ts
const users = await this.userRepo
  .createQueryBuilder("user")
  .where("user.age > :age", { age: 18 })
  .orderBy("user.name", "ASC")
  .getMany();
```

| Phương thức                             | Mô tả                  |
| --------------------------------------- | ---------------------- |
| `createQueryBuilder()`                  | Tạo truy vấn tùy chỉnh |
| `.where()`, `.andWhere()`, `.orWhere()` | Điều kiện              |
| `.leftJoinAndSelect()`                  | Join bảng khác         |
| `.getOne()`, `.getMany()`               | Lấy kết quả            |

```ts
// Ví dụ Query Builder nâng cao
const users = await this.userRepo
  .createQueryBuilder("user")
  .leftJoinAndSelect("user.posts", "post")
  .where("user.isActive = :active", { active: true })
  .andWhere("post.publishedAt IS NOT NULL")
  .orderBy("user.createdAt", "DESC")
  .take(10)
  .getMany();
```

