# <PERSON><PERSON>h với GG

- `Client` redirect đến URL truy cập `Google API`
- <PERSON>hi đăng nhập thành công `Google` sẽ redirect đến URL của `Server` cùng với `mã token` `Google` sinh ra
- `Server` dựa vào `mã token` này gọi đến `Google API` lấy thông tin `(id_token , access_token)`
- `Server` gọi lên `Google API` 1 lần nữa để lấy thông tin người dùng như `email, name, avatar, ...`
- <PERSON>u khi lấy được kiểm tra `DB` tài khoản đã tồn tại chưa,
- Nếu tồn tại thì sinh ra token trả về user, chưa thì tạo tài khoản mới và sinh tocken trả về user
- Cuối cùng redirect user về trang chủ
