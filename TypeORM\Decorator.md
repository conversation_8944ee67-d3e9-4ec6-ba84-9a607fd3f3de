# Các Decorator trong TypeORM


## 🏗️ 1. Entity Decorators - <PERSON><PERSON><PERSON> nghĩa bảng

| Decorator | <PERSON><PERSON> tả |
|----------|------|
| `@Entity()` | Đánh dấu class là một bảng trong database |
| `@ViewEntity()` | Dùng để ánh xạ tới một **view** trong database |

---

## 🔢 2. Column Decorators - Định nghĩa cột

| Decorator | Mô tả |
|-----------|------|
| `@PrimaryColumn()` | Cột chính, do bạn tự gán giá trị |
| `@PrimaryGeneratedColumn()` | Cột chính, giá trị tự tăng (auto-increment, UUID, v.v) |
| `@Column()` | Cột bình thường |
| `@CreateDateColumn()` | Tự động lưu `created_at` khi entity đư<PERSON><PERSON> tạo |
| `@UpdateDateColumn()` | Tự động cập nhật `updated_at` khi entity được cập nhật |
| `@DeleteDateColumn()` | Dùng cho soft-delete, lưu thời gian bị xóa |

---

## 🔗 3. Relation Decorators - Quan hệ giữa các bảng

| Decorator | Mô tả |
|-----------|------|
| `@OneToOne()` | Quan hệ 1-1 |
| `@OneToMany()` | Quan hệ 1-nhiều |
| `@ManyToOne()` | Quan hệ nhiều-1 |
| `@ManyToMany()` | Quan hệ nhiều-nhiều |
| `@JoinColumn()` | Định nghĩa cột khóa ngoại |
| `@JoinTable()` | Dùng với `@ManyToMany` để tạo bảng trung gian |

---

## 🧩 4. Embedded Decorator

| Decorator | Mô tả |
|-----------|------|
| `@Embedded()` *(deprecated, thay bằng `@Column(type => ...)`)* | Nhúng một class khác vào entity như một nhóm cột |

---

## 🧷 5. Indexing and Constraints

| Decorator | Mô tả |
|-----------|------|
| `@Index()` | Tạo chỉ mục (index) cho một hoặc nhiều cột |
| `@Unique()` | Ràng buộc giá trị duy nhất trên một hoặc nhiều cột |

---

## 💾 6. Listener Decorators - Lifecycle hooks

| Decorator | Mô tả |
|-----------|------|
| `@BeforeInsert()` | Trước khi entity được insert |
| `@AfterInsert()` | Sau khi insert |
| `@BeforeUpdate()` | Trước khi update |
| `@AfterUpdate()` | Sau khi update |
| `@BeforeRemove()` | Trước khi xóa (remove) |
| `@AfterRemove()` | Sau khi xóa |
| `@AfterLoad()` | Sau khi entity được load từ database |

---

## 🧪 Ví dụ

```ts
import {
  Entity, Column, PrimaryGeneratedColumn,
  CreateDateColumn, UpdateDateColumn
} from 'typeorm';

@Entity()
export class User {
  @PrimaryGeneratedColumn()
  id: number;

  @Index()
  @Column({ unique: true })
  name: string;

  // 1 `user` có nhiều `posts`
  @OneToMany(() => Post, (post) => post.user)
  posts: Post[];

  @Column({ default: true })
  isActive: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
```

- Sử dụng `relations` để load bảng liên quan

```ts
this.userRepo.find({ relations: ["posts"] });
