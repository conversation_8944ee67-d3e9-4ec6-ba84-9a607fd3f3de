# Các loại function

##  `Function cơ bản`: <PERSON><PERSON> thể hoisting
## `Callback function`: Nh<PERSON><PERSON> đối số là 1 hàm
## `Anonymous function`: thường sử dụng làm callback
## `Arrow function`

## `IFFE`: <PERSON><PERSON><PERSON> thực thi ngay
- Chỉ gọi 1 lần duy nhất, tr<PERSON>h tạo ra nhiều instance
- Tạo scope riêng

```js
//IIFE style 1
(function () {})();
```

## `Closure`: Hàm trong hàm
- Là một hàm (ghi nhớ) mà có thể truy cập biến thuộc scope chứa nó (hàm closure), ngay cả khi scope chứa nó đã được thực thi xong

```js
function ham_ben_ngoai() {
  var x = 10;

  function ham_ben_trong() {
    console.log(x); // 10
  }

  return ham_ben_trong;
}

let myFunc = ham_ben_ngoai(); // <PERSON><PERSON> chạy xong
myFunc(); // 10, vẫn lấy được x = 10 để log ra
```

Closure có 3 scope chain, đó là:

- <PERSON><PERSON> thể truy cập đến biến của chính nó (biến được định nghĩa trong dấu ngoặc nhọn của nó).
- Có thể truy cập biến của hàm bên ngoài.
- Có thể truy cập biến toàn cục (global).

