# Singleton Pattern

- <PERSON><PERSON><PERSON> bảo 1 lớp `chỉ có 1 instance duy nhất` (thể hiện duy nhất)
- <PERSON><PERSON> cấp 1 `điểm truy cập toàn cục`
- S<PERSON> dụng trong TH quản lý nguồn dữ liệu duy nhất

```js
// UserSession.js
class UserSession {
  constructor() {
    // Kiểm tra đã tồn tại instance nào chưa
    if (UserSession.instance) {
      return UserSession.instance;
    }

    // Nếu chưa khởi tạo `object user` và gán vào `UserSession.instance`
    this.user = {
      name: null,
      email: null,
    };

    UserSession.instance = this;
  }

  // Getter
  getUser() {
    return this.user;
  }

  // Setter
  setUser(user) {
    this.user = { ...this.user, ...user };
  }
}

export default new UserSession();

// Sử dụng
userSession.getUser();
userSession.setUser({ name: "<PERSON>", email: "<EMAIL>" });
```

## Ứng dụng

- Tạo 1 `Objetc` chưa `thông tin User` đang đăng nhập sử dụng cho `toàn bộ ứng dụng` tại thời điểm đăng nhập
