# Service & Dependency Injection trong Angular

## 1. Service là gì?

- Service là nơi chứa logic nghiệp vụ, giúp **chia sẻ dữ liệu hoặc xử lý logic** giữa các component.
- Tăng khả năng tái sử dụng, dễ test.

### Tạo Service

```bash
ng generate service user
# hoặc
ng g s user
```

### Ví dụ

```ts
@Injectable({
  providedIn: 'root' // Angular sẽ tự inject service này ở root injector
})
export class UserService {
  private users = ['Alice', 'Bob'];

  getUsers() {
    return this.users;
  }
}
```

### Sử dụng trong Component

```ts
@Component({...})
export class AppComponent {
  constructor(private userService: UserService) {}

  ngOnInit() {
    console.log(this.userService.getUsers());
  }
}
```

---

## 2. Dependency Injection (DI)

- <PERSON><PERSON> cơ chế **"tiêm phụ thuộc"**, giúp Angular tự động cung cấp (inject) các class/service cần thiết vào constructor.
- <PERSON><PERSON><PERSON><PERSON> quản lý bởi Angular Injector.

### Cách hoạt động

```ts
constructor(private userService: UserService) {}
```

- Angular sẽ tự tạo instance của `UserService` và truyền vào.

---

## 3. Hierarchical Injector (Phân cấp Injector)

- **Root Injector**: khi `providedIn: 'root'`, service sẽ dùng chung toàn app (singleton).
- **Component Injector**: nếu `providers: [MyService]` trong component, mỗi component có 1 instance riêng.

### Ví dụ

```ts
@Component({
  selector: 'app-child',
  templateUrl: './child.component.html',
  providers: [UserService] // tạo instance riêng cho component này
})
export class ChildComponent {
  constructor(private userService: UserService) {}
}
```

---

## 4. Lifecycle của Service

| Vị trí khai báo           | Phạm vi dùng              |
|---------------------------|---------------------------|
| `providedIn: 'root'`      | Toàn bộ app (singleton) - Không cần khai báo trong @NgModule()   |
| Trong `providers` của component | Chỉ trong component đó (và con của nó) - Cần khai báo trong @NgModule() của component sử dụng |

---

## 5. Ưu điểm của DI

- Giảm phụ thuộc cứng giữa các class
- Dễ test (mock service dễ dàng)
- Tăng khả năng mở rộng và tái sử dụng

---

## 6. Ghi nhớ

| Khái niệm              | Ý nghĩa |
|------------------------|--------|
| `@Injectable()`        | Đánh dấu class là có thể được inject |
| `providedIn: 'root'`   | Service dùng chung toàn app |
| Injector               | Hệ thống quản lý và cung cấp các dependency |