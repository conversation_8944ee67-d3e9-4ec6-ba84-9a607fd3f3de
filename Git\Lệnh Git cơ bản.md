# Lệnh Git cơ bản

## I. L<PERSON>nh git phổ biến

### 1. Git add

- `git add .` - <PERSON><PERSON><PERSON> file thay đổi vào `Staging`

### 2. Git commit

- `git commit -m "..."`
- `git commit --amend --no-edit`

### 3. Git push

- `git push origin -u branch_mane` - Push và tạo liên kết nhánh.

- `git push` - Push vào nhánh đã tạo liện kết trước đó.

- `git push -f origin branch_mane` - Push force sẽ apply toàn bộ log ở local của bạn lên branch ở repo, bất chấp log 2 nơi khác nhau. (Xóa vĩnh viễn branch cũ Push branch mới. Dễ gây conflict cho người khác cẩn trọng trước khi dùng)

### 4. Git pull (Hợp nhất code)

- `git pull origin ten_nhanh` - (tư<PERSON><PERSON>ng `fetch` và `merge`)

- `git pull --rebase origin ten_nhanh` - (tương ứng `fetch` và `rebase`)

### 5. Git reset

- `git reset .` - Ngược lại với `git add`, đưa file từ `Staging` quay lại `Change`
- `git reset [mã hash]` - Đưa file thay đổi về trạng thái `Change` `(Code chưa add)`

- `git reset --soft [mã hash]` - Đưa file thay đổi về trạng thái `Staging` `(Code đã add)`

- `git reset --hard [mã hash]` - Đưa file thay đổi về lúc chưa có gì thay đổi `(Nguy hiểm - mất code Change)`

_Sử dụng cho cả trường hợp `đã push` hoặc `chưa push`_

```js
// Vi dụ có log như sau:
// 1111 (HEAD -> test1) Git reset
// 2222 init Test1
// 3333 init
// Muốn quay trở về trước khi commit `Git reset` (vẫn giữ lại code)
git reset --soft HEAD~1 // Quay trở về trước 1 commit so với HEAD (Lúc chưa commit `Git reset`, đã add)
// Hoặc
git reset 2222
```

### 6. Git branch

- `git branch branch_mane` - Tạo nhánh

- `git branch -r` - Kiểm tra các nhánh hiện có của bạn ở local.

- `git branch -a` - Kiểm tra các nhánh hiện có trên remote repo của bạn.

- `git branch -m TenMoi` - Đổi tên nhánh hiện tại.

- `git branch -D branch_mane` - Xóa một nhánh tại local

- `git branch -d branch_mane` - Xóa một nhánh tại remote

### 7. Git checkout

- `git checkout branch_mane` - Chuyển nhánh làm việc

- `git checkout -b branch_mane` - Tạo nhánh mới dựa trên `nhánh hiện tại` và chuyển nhánh

### 8. Git stash

- `git stash` - Lưu lại các thay đổi khi chưa muốn `commit`
- `git stash list` - Xem danh sách các lần lưu
- `git stash apply stash@{n}` - Lấy ra thay đổi tại vị trí thứ `n`
- `git stash pop` - Lấy và xóa lần lưu gần nhất

## II. Lệnh git ít sử dụng

### 1. Git version (Ít dùng)

`git --v`

### 2. Git config (Ít dùng)

`git config --g user.name "Dev name"`
`git config --g user.email "Dev email"`
`git config --list`

### 3. Git mkdir (Ít dùng)

`git mkdir folder_name` - Tạo repository (File) trong hệ thống local.

`cd folder_name` - Di chuyển đến folder_name repository vừa tạo ra.

### 4. Git remote (Ít dùng)

`git remote add origin <url>` - Liên kết đến remote repository (local & GitHub)

`git remote set-url <name> <new url>` - Thay đổi địa chỉ của remote repository đã Liên kết vào địa chỉ của `<new url>`.

`git remote rename <old> <new>` - Thay đổi tên của remote repository đã Liên kết.

### 5. Git init

- `git init` (Khởi tạo git cho dự án mới của bạn)

### 6. Git status (Ít dùng)

`git status`

`git status --short`

### 7. Git diff (Ít dùng)

- `git diff` So sánh sự khác biệt kể từ lần commit cuối cùng của bạn.

### 8. Git log (Ít dùng)

- `git log --oneline` Xem lịch sử làm việc với git (lịch sử commit)

### 9. Git restore (Ít dùng)

- `git restore tenFile` - Hoàn tác các thay đổi trong 1 File

### 10. Git revert (Ít dùng)

- `git revert <commit_id> --no-edit`

Tạo commit đảo ngược, hoàn tác file về trạng thái như chưa có gì được thay đổi với mã `commit_id`.
