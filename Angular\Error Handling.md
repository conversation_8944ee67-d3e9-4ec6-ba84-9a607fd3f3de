# Error Handling

- Angular mặc định có sẵn `ErrorHandler`.
- <PERSON><PERSON> có lỗi xảy ra bên ngoài `HTTP` (UI lỗi, lỗi logic component, lỗi binding template) mà bạn không try-catch, Angular sẽ tự động chuyển lỗi này vào `ErrorHandler`. <PERSON><PERSON> nhiên có thể tự gọi để xử lý lỗi HTTP`
- <PERSON><PERSON> bạn `override ErrorHandler` bằng class `GlobalErrorHandler`, Angular sẽ tự động gọi phương thức `handleError()` của bạn thay vì mặc định

```typescript
import { ErrorHandler, Injectable } from '@angular/core';

@Injectable()
export class GlobalErrorHandler implements ErrorHandler {
  handleError(error: any): void {
    console.error('Global error:', error);
    // Gửi lỗi về server hoặc hiển thị thông báo
  }
}
```

Đăng ký trong `AppModule`:

```typescript
@NgModule({
  providers: [{ provide: ErrorHandler, useClass: GlobalErrorHandler }]
})
export class AppModule {}
```

## Tổng kết bảng so sánh

| Cấp độ lỗi                | Kỹ thuật xử lý                   |
| ------------------------- | -------------------------------- |
| HTTP API lỗi              | `catchError` + `throwError`      |
| Lỗi toàn app              | `ErrorHandler` custom            |
| Lỗi trong component logic | `try-catch`                      |
| Lỗi validate form         | `Validators`, kiểm tra `.errors` |
| Lỗi routing               | `catch` hoặc route guards        |

---