# Hàm xử lý mảng và chuỗi

## Mảng

- `splice`([vị trí bắt đầu xóa], [s<PERSON> lượng], [phần tử thay thế]): X<PERSON>a và thay thế phần tử đã xóa
- `shift`(): xóa ptu đầu tiên
- `unshift`(): Thêm phần tử vào đầu mảng
- `pop`(): X<PERSON><PERSON> phần tử cuối cùng
- `push`(): Thêm phần tử vào cuối mảng
- `join`([giá trị nối]): Nối tất cả phần tử mảng tạo thành 1 chuỗi
- `some`(): Ki<PERSON><PERSON> tra phần tử mảng có thỏa mãn đk không (Chỉ cần 1 cái đúng)
- `every`(): Kiểm tra phần tử mảng có thỏa mãn đk không (Tất cả phải thỏa mãn)
- `reduce`(): <PERSON><PERSON> dụng hàm tích tụ
- `sort`(): <PERSON><PERSON><PERSON> xếp Alphabet
- `find()`: T<PERSON><PERSON> kiếm trong mảng, trả về phần tử đó
- `filter()`: Lọc phần tử trong mảng, trả về mảng mới
- `includes()`: Kiểm tra thuộc mảng
- `indexOf()`: Tìm kiếm index của phần tử
- `slice`(start, end): tạo 1 mảng mới từ vị trí bắt đầu và kết thúc trong mảng cũ tùy chỉnh

## Chuỗi

- `split`([giá trị phân tách]): Phân tách chuỗi thành 1 mảng
- `toLowerCase()`
- `toUpperCase()`
- `trim()`
- `replace()`
- `includes()`
- `startsWith()`
- `endsWith()`
