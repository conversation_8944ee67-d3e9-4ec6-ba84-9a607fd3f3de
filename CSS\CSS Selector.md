# CSS Selector và Pseudo class

`https://viblo.asia/p/30-css-selectors-can-nho-p1-0bDM6ko6G2X4`
`https://viblo.asia/p/30-css-selectors-can-nho-p2-aKYMNjelM83E`

## I. CSS Selector

### 1. `*`

- Chọn tất cả các element

```css
* {
  margin: 0;
  padding: 0;
}
```

### 2. `X + Y` (Áp dụng thẻ đầu tiên đứng ngay sau)

- Áp dụng cho thẻ `p` đầu tiên đứng ngay sau thẻ `ul` sẽ có

```css
ul + p {
  color: red;
}
```

### 3. `X > Y` (Áp dụng thẻ con trực tiếp)

- Áp dụng cho thẻ `ul` là con trực tiếp

```css
div > ul {
  border: 1px solid black;
}
```

### 4. `X ~ Y` (Áp dụng tất cả các thẻ đứng ngay sau)

- Áp dụng cho tất cả thẻ `p` đứng ngay sau thẻ `ul`

```css
ul ~ p {
  color: red;
}
```

### 5. `X[title]`

- Áp dụng cho tất cả thẻ `a` có thuộc tính `title`

```css
a[title] {
  color: green;
}
```

## II. Pseudo class (Phần tử giả)

### 1. `X:visited` and `X:link` pseudo-class

- `:link` : trỏ đến các tag a chưa được click
- `:visted` : trỏ đến các tag a đã được click

```css
a:link {
  color: red;
}
a:visted {
  color: purple;
}
```

### 2. `X:checked`

- Áp dụng cho các `radio` hoặc `checkedbox` đã được checked

```css
input[type="radio"]:checked {
  border: 1px solid black;
}
```

### 3. `X:hover`

- Áp dụng khi element được di chuột vào

```css
div:hover {
  background: #e3e3e3;
}
```

### 4. `X:after` và `X:before`

- Append toàn bộ css đã được định nghĩa vào sau X.

```css
.clearfix:after {
  content: "";
  display: block;
  clear: both;
  visibility: hidden;
  font-size: 0;
  height: 0;
}
```

### 5. `X:nth-child(n)` và `X:nth-last-child(n)`

- Áp dụng cho các thẻ li thứ `n` (tính từ 1)

```css
li:nth-child(3) {
  color: red;
}
```

### 5. `X:first-child` và `X:last-child`

- Áp dụng cho element đầu tiên hoặc cuối cùng

```css
ul li:first-child {
  border-top: none;
}
```
