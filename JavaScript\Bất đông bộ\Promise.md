# Promise() và Promise.all()

## 1. Promise()

- `Promise` tr<PERSON> về `resolve` hoặc `reject`
- <PERSON><PERSON> 3 trạng thái:

```text
- Fulfilled: thành công → gọi resolve()
- Rejected: thất bại → gọi reject()
- Pending: đang xử lý
```

### Ví dụ

```js
// Khởi tạo Promise
var createPromise = new Promise((resolve, reject) => {
  const status
  if (status) {
    resolve("Thành công!");
  } else {
    reject("Thất bại!");
  }
});

createPromise
  .then(
    (success) => {
        //Success
    },
    (error) => {
        //Error
    };
  ).catch((err) => console.error(err));
```

- Hàm `then()` có hai 2 tham số callback là `success` và `error` nhưng bạn cũng có thể sử dụng hàm `catch` để bắt lỗi
- Nên sử dụng `.catch()` để bắt lỗi thay vì `error` của `.then()`

## 2. Promise.all()

- Nhận vào 1 mảng `Promise` và trả về kết quả ngay khi 1 `Promise` bị từ chối hoặc tất cả các `Promise` hoàn thành

```js
Promise.all([promise1, promise2])
  .then((data) => console.log(data[0], data[1]))
  .catch((error) => console.log(error));
```

## 3. Promise.allSettled()

- Nhận vào 1 mảng `Promise` và trả về kết quả khi tất cả các `Promise` hoàn thành không cần biết `resolve` hay `reject`

```js
Promise.allSettled([
  Promise.reject("This failed too."),
  Promise.resolve("Ok I did it."),
  Promise.reject("Oopps, error is coming."),
]).then((res) => {
  console.log(`Here's the result: `, res);
});
```

Kết quả: chứa `status`, `value` và `reason`

```js
[
  { status: "rejected", reason: "This failed." },
  { status: "fulfilled", : "Ok I did it." },
  { status: "rejected", reason: "Oopps, error is coming." },
];
```

## 4. Promise.race()

- Nhận vào 1 mảng `Promise`
- Trả về kết quả ngay sau khi có 1 `Promise` `resolve` hoặc `reject`

## 5. Promise.any()

- Nhận vào 1 mảng `Promise`
- Trả về `resolve`: khi 1 `Promise` resolve
- Trả về `reject`: khi tất cả `Promise` reject

## Tổng kêt

![alt](https://topdev-vn.cdn.ampproject.org/ii/AW/s/topdev.vn/blog/wp-content/uploads/2023/01/Promise.jpg)
