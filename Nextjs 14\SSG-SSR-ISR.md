# SSG, SSR, ISR

| Đặc điểm             | **Static Rendering - SSG**       | **ISR**                              | **Dynamic Rendering - SSR**                             |
| -------------------- | ------------- | ------------------------------------ | ----------------------------------- |
| Tốc độ tải           | ⚡ Rất nhanh   | ⚡ Nhanh                              | 🐢 Chậm hơn                         |
| SEO tốt              | ✅             | ✅                                    | ✅                                   |
| Lấy dữ liệu khi nào? | Khi build     | Khi build + tự cập nhật sau đó       | Mỗi request                         |
| Cập nhật dữ liệu     | ❌             | ✅ Sau mỗi `revalidate` giây          | ✅ Luôn                              |
| Dùng cho             | Nội dung tĩnh | Nội dung thay đổi không thường xuyên | Nội dung cá nhân, thay đổi liên tục |

# Lưu ý

- Next 14 mặc định `fetch()` là { cache: 'force-cache'} nghĩa là `SSG` khi dùng `fetch()`

```text
Chạy npm run build để kiểm tra các page đang thuộc loại nào
```

- Trong Next chỉ `Server component` bị ảnh hưởng bởi `SSR`
- Page tự động chuyển sang `SSR` khi ta dùng `Dynamic function` trong component tree (children, parent component, layout, ...): `cookies()`, `header()`, `searchParams`

```text
Cách xử lý để không bị chuyển thành SSR:
- cookies() || header(): Không sử dụng nữa, thay vào đó lưu vào `localStorage` hoặc dùng trong `use-client`
- searchParams: Bọc Component sử dụng `searchParams` bằng thẻ `<Suspense>` hoặc dùng trong `use-client` với useSearchParams()
```

## param() và searchParams()

- `searchParams()`: `?a=1&b=2` (query parameters)
- `param()`: `products/1` (dynamic route parameters)
