# Các loại Hook

## 1. useState()

- React `nhóm tất cả` c<PERSON><PERSON> lầ<PERSON> `setState` lại thành 1 lần `reRender` gọi là `batching`
- Nên khi `setState` và gọi lại giá trị ngay sau đó thì chưa nhận được giá trị mới vì componet chưa `reRender`

```js
import { useState } from 'react';

const [address, setAddress] = useState({
  nation: "VN",
  city: "HCM",
});

// Phải set Immutable như bên dưới
const handleSetState() => {
  setAddress(prev => ({
  ...prev,
  city: 'HN'
  }))
}
```

- Sử dụng `flushSync` để hủy bỏ `batching`, ép React re-render DOM ngay sau khi mỗi lần setState
- Lưu ý: chỉ cập nhập DOM tại thơi điểm đó, state vẫn phải `batching`

```js
import { flushSync } from 'react-dom';

const handleClick() => {
    flushSync(() => {
      setData((d) => d + 1)
    })
    // DOM đã được cập nhập và hiển thị data mới set
    console.log(data) // Tuy nhiên data vẫn là dữ liệu cũ
}

// Tại đây là dữ liệu mới vì đã hoàn thành batching
console.log(data)
```

## 2. useEffect()

- Chạy sau khi `Paint` trên trình duyệt (Vẽ từng pixel lên trình duyệt)
- Dependency dựa vào `tham chiếu` của biến thay đổi
- Cẩn thận với Dependency truyền vào tránh lặp vô hạn khi `reRender`
- `array` và `object` là `tham chiếu` phải cẩn thận ki truyền vào `Dependency`

```js
useEffect(() => {
  // Sử dụng prev để không phải truyền address vào dependency
  // Để khi setAddress trong useEffect không bị gọi lại (lặp vô hạn)
  setAddress((prev) => ({
    ...prev,
    city: "HN",
  }));

  return () => {
    // handle prev destroy
  };
}, []);
```

## 3. useContext()

```js
// Khởi tạo Context với `createContext`
export const DataContext = createContext({
  data1: "text1",
  data2: "text2",
});

// Bọc component cần nhận data từ context với `Provider`
export default function MyApp() {
  return (
    <DataContext.Provider
      value={{
        data1,
        data2,
      }}
    >
      <Form />
    </DataContext.Provider>
  );
}

// Gọi data từ context vơi `useContext`
const getDataContext = useContext(DataContext);
```

## 4. React.memo() - HOC

- Tránh component reRender khi componenet cha reRender
- Check props truyền vào để quyết định reRender
- `Props` truyền vào mà là 1 `object` thì luôn `reRender` vì `tham chiếu` luôn thay đổi (Cần sử dụng hàm equal để check)
- Nếu props là `object/array/function` → nên memo hóa bằng `useMemo`, `useCallback` trước khi truyền xuống để tránh re-render không cần thiết.

```js
function Title(props) {
  return <h1>Test data</h1>;
}

// Hàm cho phép quản lý so sánh props để chech reRender
function equal(prevProp, nextProp) {
  // Check props, return false sẽ reRender
  return true;
}

export default React.memo(Title, equal);
```

## 5. useMemo()

- Tránh việc tính toán lại 1 `function` mỗi khi Component chứa nó reRender
- Sử dụng cho `biến` tính toán
- Sử dụng cho các `biến` khai báo object, array truyền cho component con
- Kết hợp với `React.memo()` để không cần hàm `equal` so sánh

```js
// Biến address là 1 tham chiếu, sẽ thay đổi khi componenet reRender
// Khiến cho component con nhận nó làm props sẽ bị reRender khi copmponent cha reRender
const address = useMemo(() => {
  return {
    street: "380 Lac Long Quan",
  };
}, [a, b]);
```

```text
Dependency thay đổi mà không gọi lại do Dependency bị mutate (thay đổi giá trị nhưng không thay đổi tham chiếu)
```

## 6. useCallback()

- Tránh 1 `function` bị gọi lại không cần thiết mỗi khi component `reRender`
- `funtion` là 1 `object` => tham chiếu. Nên mỗi khi component `reRender` thì sẽ có 1 tham chiếu mới

```js
const handleClick = useCallback(() => {
  return {
    street: "380 Lac Long Quan",
  };
}, [a, b]);
```

## 7. useLayoutEffect()

- Chạy hàm callback trước khi `Paint` trên trình duyệt
- Dùng khi UI bị flicker như TH ở dưới

```js
const handleClick = () => {
  setCount((prev) => prev + 1);
};

// Với useEffect(), trên UI khi setCount(4) sẽ hiển thị 4 và nhảy về 0
// Vì khi setCount(4), sẽ reRender => Pain được vẽ hiển thị 4
// Pain vẽ xong callback useEffect mới được gọi để setCount(0);
useEffect(() => {
  if (count === 4) {
    setCount(0);
  }
}, [count]);

// Với useLayoutEffect(), trên UI khi setCount(4) sẽ hiển thị 0 ngay lập tức
// Vì khi setCount(4), sẽ reRender => callback useLayoutEffect sẽ được gọi trước khi `Pain` và setCount(0);
// Sau đó reRender UI hiển thị 0
useLayoutEffect(() => {
  if (count === 4) {
    setCount(0);
  }
}, [count]);
```

```text
- Quá trình cập nhập state:
* reRender
* Gọi useMemo, useCallback
* Render JSX
* Cập nhập DOM ảo
* Cập nhập DOM thật (tại đây có thể truy cập DOM)
* Chạy Callback useLayoutEffect()
* Paint UI
* Chạy Callback useEffect()
* Hiển thị UI cho User
```

## 8. useImperativeHandle() kết hợp với forwardRef

- Sử dụng để truyền ref từ `con` ra `cha`
- Tại `cha` gọi được các hàm mà trong con tryền ra ngoài thông qua ref

```js
// Tại component Cha
export default function Parent() {
  const myInputRef = useRef();

  return (
    <div>
      <Child ref={myInputRef} />
      <button onClick={() => myInputRef.current.focus()}>Focus</button>
      <button onClick={() => myInputRef.current.clear()}>Clear</button>
    </div>
  );
}
// gọi được focus và clear của <input> trng component <Child> qua myInputRef.current
-------------------------------------

// Tại component Con
const Child = forwardRef((props, ref) => {
  const inputRef = useRef();

  useImperativeHandle(ref, () => ({
    focus: () => {
      inputRef.current.focus();
    },
    clear: () => {
      inputRef.current.value = '';
    }
  }));

  return <input ref={inputRef} />;
});
// Đưa focus và clear của <input> trong child ra ngoài cho parent sử dụng
```

## 9. useId()

- Tự động sinh ID duy nhất mỗi lần render component (k phải mỗi lần re-render)
- ID ổn định trong suốt vòng đời component

```js
const id = useId();
```

## 10. React Portal

- Đặt component đến thẻ mong muốn trong DOM

```js
ReactDOM.createPortal(child, container);

// child: Là component muốn hiển thị
// container: Là nơi component được đặt (DOM)
```

- Ví dụ:

```js
import React from "react";
import ReactDOM from "react-dom";

function Modal({ children }) {
  return ReactDOM.createPortal(
    <div id="modal-wrapper">{children}</div>,
    document.querySelector("body")
  );
}

export default Modal;
```

- Nội dung khi component `Modal` hiển thị trên DOM (Nằm trong thẻ `body`)

```tsx
<html>
  <head></head>
  <body>
    <div id="root"></div>
    <div id="modal-wrapper"></div> // Tự động hiển thị tại đây
  </body>
</html>
```

## 11. useSyncExternalStore

- Đọc state từ store bên ngoài `React` `(Redux, Zustand, ...)`, đảm bảo hoạt động đúng trong `Concurrent`

## 12. useInsertionEffect

- Chèn CSS styles đồng bộ vào DOM trước khi vẽ `Paint`
- Chạy trước `useLayoutEffect`

----------------------------------------------------------------------

## 🔄 Concurrent Features `createRoot()`

## 1. useTransition() và startTransition

- Đánh dấu 1 cập nhập là không khẩn cấp để ưu tiên các hành động khác (setState)
- `useTransition()` Chứa isPending
- Tránh block UI

```tsx
const [input, setInput] = useState("");
const [list, setList] = useState([]);
const [isPending, startTransition] = useTransition();

const handleChangeValue = (e) => {
  const value = e.target.value;
  setInput(value);

  startTransition(() => {
    const filtered = [];
    for (let i = 0; i < 10000; i++) {
      filtered.push(`${value} - ${i}`);
    }
    setList(filtered);
  });
};

// Có useTransition(): setInput() -> render UI -> setList() -> render UI
// Không có useTransition(): [setInput() && setList()] -> render UI 
```

## 2. useDeferredValue()

- Tương tự useTransition(), thay vì trì hoãn hành động thì trì hoãn cập nhập giá trị
- Cập nhập giá trị mới khi rảnh để tránh tính toán nhiều không cần thiết

```tsx
const [input, setInput] = useState('');
const deferredCount = useDeferredValue(input);

const list = Array(10000).fill(0).map((_, i) => `Item ${i}`);
const filteredList = list.filter(item => item.includes(deferredInput));

// input: được cập nhập ngay khi gõ
// deferredCount trì: hoãn cập nhập giá trị của `input` cho đến khi rảnh
```

----------------------------------------------------------------------

## ✅ ReactJs 19

## 1. useActionState

- Quản lý form trực tiếp

```tsx
function App() {
  const [data, submitAction, isPending] = useActionState(
    // Khi submitAction thì callback này được chạy và trả về dữ liệu vài biến `data`
    (prevState, formData) => {
      return {
        name: formData.get("name"),
      };
    },
    // Init data form
    {
      name: "Quan",
    }
  );

  return (
    <div>
      <form action={submitAction}>
        <input type="text" name="name" />
        <button type="submit" disabled={isPending}>
          Update
        </button>
      </form>
    </div>
  );
}
```

## 2. useOptimistic()	
- Dự đoán UI trước khi server trả về kết quả (UX tốt hơn)

## 3. useFormStatus()	
- Hook giúp biết trạng thái pending của form

## 4. useFormState()	
- Hook quản lý state của form
