# Các lo<PERSON>i Hook

## 1. useState()

- React `nhóm tất cả` c<PERSON><PERSON> lầ<PERSON> `setState` lại thành 1 lần `reRender` gọi là `batching`
- Nê<PERSON> khi `setState` và gọi lại giá trị ngay sau đó thì chưa nhận được giá trị mới vì componet chưa `reRender`

```js
import { useState } from 'react';

const [address, setAddress] = useState({
  nation: "VN",
  city: "HCM",
});

// Phải set Immutable như bên dưới
const handleSetState() => {
setAddress(prev => ({
...prev,
city: 'HN'
}))
}
```

- Sử dụng `flushSync` để hủy bỏ `batching`, ép React reRender ngày sau khi mỗi lần setState

```js
import { flushSync } from 'react-dom';

const handleClick() => {
    flushSync(() => {
        setData1((d) => d + 1)
    })
    console.log(data1) // Tại đây Data1 vẫn là giá trị cũ (lúc chưa setData1)
    flushSync(() => {
        console.log(data1) // Tại đây Data1 vẫn là giá trị cũ (lúc chưa setData1)
        setData2((t) => t + 2)
    })
}

// Tại đây log giá trị mới sau khi setData1 và sẽ log lần 2 sau setData2
console.log(data1)
```

## 2. useEffect()

- Quản lý vòng đời Component
- Chạy sau khi `Paint` trên trình duyệt (Vẽ từng pixel lên trình duyệt)
- Dependency dựa vào giá trị thay đổi `tham chiếu`
- Cận thận với Dependency truyền vào tránh lặp vô hạn khi reRender
- `array` và `object` là `tham chiếu` phải cẩn thận ki truyền vào `Dependency`

```js
useEffect(() => {
  // Sử dụng prev để không phải truyền address vào dependency
  // Để khi setAddress trong useEffect không bị gọi lại (lặp vô hạn)
  setAddress((prev) => ({
    ...prev,
    city: "HN",
  }));

  return () => {
    // handle prev destroy
  };
}, []);
```

## 3. useContext()

```js
// Khởi tạo Context với `createContext`
export const DataContext = createContext({
  data1: "text1",
  data2: "text2",
});

// Bọc component cần nhận data từ context với `Provider`
export default function MyApp() {
  return (
    <DataContext.Provider
      value={{
        data1,
        data2,
      }}
    >
      <Form />
    </DataContext.Provider>
  );
}

// Gọi data từ context vơi `useContext`
const getDataContext = useContext(DataContext);
```

## 4. React.memo() - HOC

- Tránh component reRender khi componenet cha reRender
- Check props truyền vào để quyết định reRender
- `Props` truyền vào mà là 1 `object` thì luôn `reRender` vì `tham chiếu` luôn thay đổi (Cần sử dụng hàm equal để check)

```js
function Title(props) {
  return <h1>Test data</h1>;
}

// Hàm cho phép quản lý so sánh props để chech reRender
function equal(prevProp, nextProp) {
  // Check props, return false sẽ reRender
  return true;
}

export default React.memo(Title, equal);
```

## 5. useMemo()

- Tránh việc tính toán lại 1 `function` mỗi khi Component chứa nó reRender
- Sử dụng cho `biến` tính toán
- Sử dụng cho các `biến` khai báo object, array truyền cho component con
- Kết hợp với `React.memo()` để không cần hàm `equal` so sánh

```js
// Biến address là 1 tham chiếu, sẽ thay đổi khi componenet reRender
// Khiến cho component con nhận nó làm props sẽ bị reRender khi copmponent cha reRender
const address = useMemo(() => {
  return {
    street: "380 Lac Long Quan",
  };
}, [a, b]);
```

```text
Dependency thay đổi mà không gọi lại do Dependency bị mutate (thay đổi giá trị nhưng không thay đổi tham chiếu)
```

## 6. useCallback()

- Tránh 1 `function` bị gọi lại không cần thiết mỗi khi component `reRender`
- `funtion` là 1 `object` => tham chiếu. Nên mỗi khi component `reRender` thì sẽ có 1 tham chiếu mới

```js
const handleClick = useCallback(() => {
  return {
    street: "380 Lac Long Quan",
  };
}, [a, b]);
```

## 7. useLayoutEffect()

- Chạy hàm callback trước khi `Paint` trên trình duyệt
- Dùng khi UI bị flicker như TH ở dưới

```js
const handleClick = () => {
  setCount((prev) => prev + 1);
};

// Với useEffect(), trên UI khi setCount(4) sẽ hiển thị 4 và nhảy về 0
// Vì khi setCount(4), sẽ reRender => Pain được vẽ hiển thị 4
// Pain vẽ xong callback useEffect mới được gọi để setCount(0);
useEffect(() => {
  if (count === 4) {
    setCount(0);
  }
}, [count]);

// Với useLayoutEffect(), trên UI khi setCount(4) sẽ hiển thị 0 ngay lập tức
// Vì khi setCount(4), sẽ reRender => callback useLayoutEffect sẽ được gọi trước khi `Pain` và setCount(0);
// Sau đó reRender UI hiển thị 0
useLayoutEffect(() => {
  if (count === 4) {
    setCount(0);
  }
}, [count]);
```

```text
- Quá trình cập nhập state:
* reRender
* Gọi đến hàm useEffect() và useLayoutEffect()
* Render JSX
* Cập nhập DOM ảo
* Cập nhập DOM thật (tại đây có thể truy cập DOM)
* Chạy Callback useLayoutEffect()
* Paint UI
* Chạy Callback useEffect()
* Hiển thị UI cho User
```

## 8. useImperativeHandle() kết hợp với forwardRef

- Sử dụng để đưa component `con` ra component `cha` thông qua ref
- Tại `cha` gọi component ở `con` thông qua ref để thực thi

```js
// Tại component Cha
const inputRef = useRef(null)

handleClick() {
  // Data trả về trong hàm useImperativeHandle() ở Con
  console.log(inputRef.current)
}

// Tại component Con
useImperativeHandle(
  ref, // ref của cha truyền xuống con
  () => {
    return {
      // Data trả về khi cha gọi `inputRef.current`
    };
  },
);
```

```text
- Từ cha gọi component của con: useImperativeHandle() và ref
- Từ con gọi component của cha: props cha truyền component xuống con
```

## 9. useId()

```js
const id = useId();
```

## 10. React Portal

- Đặt component đến thẻ mong muốn trong DOM

```js
ReactDOM.createPortal(child, container);

// child: Là component muốn hiển thị
// container: Là nơi component được đặt (DOM)
```

- Ví dụ:

```js
import React from "react";
import ReactDOM from "react-dom";

function Modal({ children }) {
  return ReactDOM.createPortal(
    <div id="modal-wrapper">{children}</div>,
    document.querySelector("body")
  );
}

export default Modal;
```

- Nội dung khi component `Modal` hiển thị trên DOM (Nằm trong thẻ `body`)

```html
<html>
  <head></head>
  <body>
    <div id="root"></div>
    <div id="modal-wrapper"></div>
  </body>
</html>
```

## 11. useDebugValue()

## 12. useTransition() và startTransition (ReactJs 19)

- Tránh block UI
- Chứa isPending (Không cần tạo state loading nữa)
- ReactJs 19 cos thể dùng `async` trong `startTransition`

```tsx
const [isPending, startTransition] = useTransition();
```

## 13. useActionState (ReactJs 19)

- Quản lý form trực tiếp

```tsx
function App() {
  const [data, submitAction, isPending] = useActionState(
    // Khi submitAction thì callback này được chạy và trả về dữ liệu vài biến `data`
    (prevState, formData) => {
      return {
        name: formData.get("name"),
      };
    },
    // Init data form
    {
      name: "Quan",
    }
  );

  return (
    <div>
      <form action={submitAction}>
        <input type="text" name="name" />
        <button type="submit" disabled={isPending}>
          Update
        </button>
      </form>
    </div>
  );
}
```
