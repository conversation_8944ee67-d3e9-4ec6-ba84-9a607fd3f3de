# Docker

- `Docker` là nền tảng `ảo hóa` cho phép đóng gói ứng dụng vào 1 `container` độc lập với máy chủ.
- `Docker` giúp đồng bộ môi trường giữa các máy chủ, giúp chúng ta dễ dàng chuyển đổi giữa các môi trường khác nhau.

## I. Image vs Container

- `Docker Image` là phần mềm được đóng gói. (`Dockerfile`, `docker build`)
- `Docker Container` là một `instance` của `Docker Image`. Một `Docker Image` có thể tạo ra nhiều `Docker Container`.

## II. Lệnh Docker

### Thông tin Docker

```bash
docker version
```

### Show các Image/Container

```bash
# Show Image
docker image ls

# Show container đang hoạt động (Show cả container dừng thêm `-a`)
docker container ls
# Hoặc
docker ps
```

### Xóa Image/Container

```bash
# Xóa Image
docker image rm HASH

# Xóa Container
docker container rm HASH
```

### Dừng container

```bash
docker container stop HASH
```

### Show log của container

```bash
docker logs -f HASH_CONTAINER
```

### Build `Image` từ `Dockerfile`

```text
Chú ý
Image được build ở mỗi máy sẽ có sự khác nhau. Vì thế nên build trong CICD để build trên cùng môi trường có sẵn
```

- `anhquan219/my-app:v0` là tên `Image`, đặt tên theo cú pháp `USERNAME/TÊN_IMAGE:TAG`

```bash
docker build --progress=plain -t anhquan219/my-app:v0 -f Dockerfile.dev .
```

Nếu muốn chỉ định file `Dockerfile` nào đó thì thêm `-f` vào đường dẫn tới file đó.

Thi thoảng sẽ có thể gặp lỗi do cache, vậy thì thêm `--no-cache` vào

### Tạo và chạy `container` dựa trên `Image`

```bash
docker container run -dp PORT_NGOAI:PORT_TRONG_DOCKER TEN_IMAGE
```

ví dụ

```bash
docker container run -dp 3000:3000 anhquan219/my-app:v0
```

- Nếu muốn mapping `folder trong container` và `folder ở local` thì thêm `-v`. Cái này gọi là `volume`.
- Giúp cho khi có file ảnh up lên thì code cũng có file ảnh đó

```bash
docker container run -dp 3000:3000 -v ~/Documents/DuocEdu/NodeJs-Super/Twitter/uploads:/app/uploads anhquan219/my-app:v0
```

### Truy cập vào terminal của container

```bash
docker exec -it HASH_CONTAINER sh
```

Muốn thoát ra thì gõ `exit`

## Lệnh khác

- Dừng và xóa hết tất cả `container` đang chạy

```bash
docker stop $(docker ps -aq) && docker rm $(docker ps -aq)
```

- Thêm chế độ tự động khởi động lại `container` khi `reboot server`. Trong trường hợp đã có `container` từ trước thì dùng

```bash
docker update --restart unless-stopped HASH_CONTAINER
```

- Còn chưa có `container` thì thêm vào câu lệnh `docker run` option là `--restart unless-stopped`

```bash
docker run -dp 3000:3000 --name twitter-clone --restart unless-stopped -v ~/twitter-clone/uploads:/app/uploads duthanhduoc/twitter:v4
```

## III. Docker hub

### Đăng nhập docker hub trên terminal

```bash
docker login
# Hoặc
docker login -u <username> -p <password>
```

### Đẩy Image lên docker hub

- Trước khi đẩy trên Docker Hub cần tạo Repository (ví dụ Repository: `anhquan219/my-app`)

```bash
# Cùng tên với Image trên Docker Hub
docker push [new-repo:tagname]

# Khác tên
docker tag [local-image:tagname] [new-repo:tagname]
docker push [new-repo:tagname]
```

Ví dụ

```bash
docker push anhquan219/my-app:v0
```

### Kéo image về local

```bash
docker pull [new-repo:tagname]

# Ví dụ
docker pull anhquan219/my-app:v0
```
