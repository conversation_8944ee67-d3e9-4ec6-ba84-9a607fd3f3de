# Generic type

- Truyền Type động vào hàm giống như truyền đối số cho hàm
- Truyền thông qua `<>`

```ts
// Hàm nhận Type `T` và giá trị `token` kiểu `string` và trả `kêt quả` kiểu `T`
const custom = <T>(token: string) => {
  return token as T;
};

// Sử dụng
const payload = custom<Payload>(sessionToken);

// Truyền type Payload vào hàm custom => T được gắn là Payload => `token as Payload`
```

Ví dụ 2:

```ts
// Hàm nhận Type T và giá trị `value` kiểu T và trả `kêt quả` kiểu T
function identity<T>(value: T): T {
  return value;
}
```
