# Con trỏ This

`https://codelearn.io/sharing/con-tro-this-trong-javascript`
`https://www.youtube.com/watch?v=ii1Ra_zLDIo`

## 1. Trong Phương thức

- `Đại diện` cho một `đối tượng` mà `nó thuộc về`
- `Đ<PERSON><PERSON> diện` cho một `đối tượng` truy cập `phương thức` đó

```js
const person = {
  firstName: "Quan",
  lastName: "Nguyen",
  // Phương thức
  showName: function () {
    console.log(this); // this: đại diện cho `person` nó thuộc về
  },
  child: {
    name: "123",
    // Phương thức
    show: function () {
      console.log(this); // this: đại diện cho `child` nó thuộc về
    },
  },
};

console.log(person.showName()); // this: đại diện cho `person` g<PERSON><PERSON> nó
console.log(person.child.showName()); // this: đạ<PERSON> di<PERSON>n cho `child` g<PERSON><PERSON> nó
```

## 2. <PERSON><PERSON><PERSON><PERSON> thức

- `Đ<PERSON><PERSON> di<PERSON>n` cho `đối tượng` `global`

```tsx
// Hàm thông thường
function Car(name) {
  console.log(this); // `this` đại diện cho `windown`
}

Car(); // Tương đương windown.Car()

// Ví dụ khác
function Car(name) {
  // Phương thức
  this.run = function () {
    // Hàm bình thường
    function test() {
      console.log(this); // `this` đại diện cho `windown`
    }
  };
}
```

## 3. Trong hàm tạo

- `Đại diện` cho `đối tượng` sẽ `được tạo`

```tsx
// Hàm tạo
function Car(name) {
  this.name = name; // `this` đại diện cho đối tượng được tạo (vinfast)
}

// Tạo đối tượng `vinfast`
const vinfast = new Car("vinfast");
```

## 4. Truyền vào Callback

- Nếu hàm được truyền vào như 1 `callback` thì `this` không đại diện cho `object` đó nữa. Cần sử dụng `anonymous function` hoặc `bind()`

```js
var person = {
  firstName: "Khoa",
  lastName: "Nguyen",
  showName: function () {
    console.log(this.firstName + " " + this.lastName);
  },
};

$("button").click(person.showName); // showName truyền vào như callback, ở đây this chính là button

// Dùng anonymous function
$("button").click(function () {
  person.showName();
});

// Dùng bind
$("button").click(person.showName.bind(person)); //this ở đây vẫn là object person
```
