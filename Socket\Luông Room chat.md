# Luồng chat

## Client

- <PERSON>án `Authorization` vào socket trước khi connect. <PERSON><PERSON><PERSON> đích phân biệt các users đang connect

```js
socket.auth = {
  Authorization: `Bearer ${localStorage.getItem("access_token")}`,
};
```

- Send message chứa `sender_id` và `receiver_id`

```js
const conversation = {
  content: value,
  sender_id: profile._id,
  receiver_id: receiver,
};

socket.emit("send_message", {
  payload: conversation,
});
```

- Nhận message

```js
socket.on("receiver_message", (data) => {});
```

## Server

- Sử dụng Middlewares để xử lý `Authorization` Client gửi lên. Convert thành `decoded_authorization` và `access_token`
- `decoded_authorization` chứa `user_id`

```js
io.use(async (socket, next) => {
  const { Authorization } = socket.handshake.auth; // Client gửi Authorization lên
  const access_token = Authorization?.split(" ")[1];
  const { user_id } = socket.handshake.auth.decoded_authorization as TokenPayload;

  // Join vào room user_id của bản thân
  socket.join(user_id);

  socket.on("send_message", async (data) => {
    const { content, sender_id, receiver_id } = data.payload;
    socket.to(receiver_id).to(user_id).emit("receiver_message", {
      payload: conversation,
    });
  });
});
```

## Tóm tắt

### Tại Client

- Người dùng gắn `access_token` vào `socket.auth`
- Sau đó thực hiện `socket.connect()`
- Emit message chưa `user_id` người nhận

### Tại Server

- `Io` bắt sự kiện `connection`
- Lấy được `access_token` từ `socket.handshake.auth`, và decode lấy được `user_id`
- Join vào room `user_id`: `socket.join(user_id)`
- Khi bắt được sự kiện `send_message`, lấy được `receiver_id`
- Thực hiện gửi message đó đến người nhận thông qua
  `socket.to(receiver_id).to(user_id).emit()`
- Gửi đến `user_id` để khi bật nhiều tab thì các tab đều nhận được sự kiện
