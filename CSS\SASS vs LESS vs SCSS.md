# SASS vs LESS vs SCSS

## SASS và SCSS (Ruby), LESS (NodeJs)

- <PERSON><PERSON> chương trình tiền xử lý `CSS`
- `SCSS` là cách viết khác của `SASS`
- `SASS` sử dụng thụt đầu dòng thay vì ngoặc {} như `SCSS`
- Biên dịch bằng `Webpack`
  `SASS và LESS có các tính năng gần giống nhau, chỉ khác nhau cách viết`

## Các tính năng `SCSS`

### Xếp chồng - Nested Rules

```scss
/* SCSS */
li {
  display: inline-block;
  a {
    color: red;
  }

/* CSS */
li {
    display: inline-block;
}

li a {
    color: red;
}
```

### Biến

```scss
// Khởi tạo
$RedColor: red;

a {
  color: $RedColor; // Sử dụng
}
```

### Mixi (hiểu như là function)

```scss
// Khởi tạo
@mixin colorVsStyle($color) {
  color: $color;
  font-style: italic;
}

a {
  @include colorVsStyle(red); // Sử dụng
}
```

### Kế thừa

```scss
.class-color-text {
  color: red;
}

a {
  @extend .class-color-text; // Sử dụng
}
```

### Vòng lặp

```scss
// For
@for $i from 1 through 3 {
  $data: $i + 3;

  .padding-#{$i} {
    padding: $data;
  }
}
```

```scss
// while
@while $i < 3 {
  $data: $i + 3;

  .padding-#{$i} {
    padding: $data;
  }
}
```

```scss
// each
@each $i in (normal, bold, italic) {
  .#{$i} {
    front-weight: $i;
  }
}

// Kêt quả
.normal {
  front-weight: normal;
}
.bold {
  front-weight: bold;
}
.italic {
  front-weight: italic;
}
```

### Mệnh đề điều kiện if

```scss
$test: 5;

p {
  @if $test < 3 {
    color: red;
  } @else {
    color: blue;
  }
}
```
