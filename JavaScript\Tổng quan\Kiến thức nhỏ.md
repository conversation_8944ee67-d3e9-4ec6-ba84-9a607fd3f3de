# Kiến thức nhỏ

## 1. So sánh `==` và `===`

- `==`: <PERSON><PERSON> <PERSON>p kiểu nếu cần (lỏng lẻo)
- `===`: Không ép kiểu (nghiêm ngặt)

```js
// Ví dụ về ==
5 == "5"; // true (string "5" ép kiểu thành number 5)
5 === "5"; // false (khác kiểu: number vs string)
```

## 2. So sánh `||` và `??`

`string rỗng là false`

- `||`: bỏ qua các giá trị falsy (false, 0, "", null, undefined, NaN)
- `??`: chỉ bỏ qua null và undefined

```js
let a = 0 || 100;   // 100
let b = 0 ?? 100;   // 0 ✅
```

## 3. Strict mode

`"use strict";`

- Bắt lỗi khi dùng biến chưa khai báo
- Không cho gán giá trị cho từ khóa dự trữ
- Không thể sử dụng 1 biến mà không khai báo từ kháo (const, let, var)

## 4. Async/await trong Vòng lặp

- `for`, `for...of`, `for...in` : Chạy tuần tự và `có` chờ `Promise`
- `ForEach`: Chạy tuần tự nhưng `không` chờ `Promise`

## 4. `3 Cách Clone Object`

- Clone lông:
  Sử dụng `...`
  Sử dụng `Object.assign()`
- Clone sâu:
  Sử dụng: `JSON`
  Sử dụng: `cloneDeep` của `lodash`
- Lưu ý: Sử dụng: `JSON` không clone được `method`

## 5. map() và forEach()

| Tiêu chí         | `map()`                      | `forEach()`                    |
| ---------------- | ---------------------------- | ------------------------------ |
| Trả giá trị      | ✅ Mảng mới                   | ❌ Không                        |
| Break được không | ❌ Không                      | ❌ Không                        |
| Tốc độ           | Nhanh hơn `forEach` một chút | Chậm hơn                       |
| Dùng khi nào     | Khi cần **tạo mảng mới**     | Khi cần **duyệt mà không trả** |


## 6. `Tham chiếu` và `Tham trị`

### Tham trị (value type)

- Lưu trữ giá trị
- Khi gán sẽ tạo 1 bản sao gán vào biến mới. Khi biến gốc thay đổi không ảnh hưởng đến biến được gán
- Kiểu dữ liệu: `number, string, boolean, null, undefined`

### Tham chiếu (Refenrence type)

- Lưu trữ địa chỉ
- Khi gán sẽ tham chiếu đến ô nhớ gốc. Khi biến gốc thay đổi sẽ ảnh hưởng đến biến được gán
- Kiểu dữ liệu: array, object

## 7. Null, Undefined, NaN

### Undefined

| Giá trị     | typeof        | Dùng khi                                    |
| ----------- | ------------- | ------------------------------------------- |
| `undefined` | `"undefined"` | Biến khai báo chưa gán                      |
| `null`      | `"object"`    | Cố tình gán giá trị rỗng, không tồn tại     |
| `NaN`       | `"number"`    | Kết quả không phải số (vd: `parseInt('a')`) |

## 8. Tham số và đối số

| Tên tiếng Việt | Tên tiếng Anh | Giải thích                                                                      |
| -------------- | ------------- | ------------------------------------------------------------------------------- |
| Tham số        | Parameter     | Là **biến đại diện** được định nghĩa trong hàm, dùng để nhận giá trị truyền vào |
| Đối số         | Argument      | Là **giá trị thực tế** truyền vào hàm khi gọi hàm                               |


```js
// a và b là tham số
function getSum(a, b) {
  return a + b;
}
// 1 và 2 là đối số
sum(1, 2);
```
