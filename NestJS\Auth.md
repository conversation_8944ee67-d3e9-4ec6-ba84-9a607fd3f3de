# 🔐 JWT Authentication trong NestJS

## ✅ 1. JWT là gì?

- Sau khi user đăng nhập, server trả về 1 `token`.
- Client lưu `token` (thường trong `localStorage`), g<PERSON>i kèm mỗi `request`.
- Server x<PERSON>c thực `token` → cho phép truy cập.

---

## ✅ 2. Cài đặt thư viện

```bash
npm install @nestjs/jwt @nestjs/passport passport passport-jwt
npm install --save-dev @types/passport-jwt
```

---

## ✅ 3. C<PERSON>u trúc JWT Auth Module

```bash
src/
├── auth/
│   ├── auth.module.ts
│   ├── auth.service.ts
│   ├── auth.controller.ts
│   ├── jwt.strategy.ts
│   └── dto/
│       ├── login.dto.ts
│       └── register.dto.ts
```

---

## ✅ 4. Cấu hình JWT

### 📁 auth.module.ts

```ts
@Module({
  imports: [
    JwtModule.register({
      secret: process.env.JWT_SECRET, // khóa bí mật tại .env
      signOptions: { expiresIn: "1h" }, // thời gian sống
    }),
    PassportModule,
    TypeOrmModule.forFeature([User]),
  ],
  providers: [AuthService, JwtStrategy],
  controllers: [AuthController],
})
export class AuthModule {}
```

---

## ✅ 5. Tạo token khi login

### 📁 auth.service.ts

```ts
@Injectable()
export class AuthService {
  constructor(
    @InjectRepository(User)
    private userRepo: Repository<User>,
    private jwtService: JwtService
  ) {}

  async validateUser(email: string, password: string): Promise<User | null> {
    const user = await this.userRepo.findOne({ where: { email } });

    // ⚠️ Production: Sử dụng bcrypt để hash password
    // const isPasswordValid = await bcrypt.compare(password, user.hashedPassword);
    if (user && user.password === password) {
      const { password, ...result } = user;
      return result as User;
    }
    return null;
  }

  async login(user: User) {
    const payload = { sub: user.id, email: user.email };
    return {
      access_token: this.jwtService.sign(payload),
    };
  }
}
```

---

## ✅ 6. Controller đăng nhập

### 📁 auth.controller.ts

```ts
@Controller("auth")
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post("login")
  async login(@Body() body: { email: string; password: string }) {
    const user = await this.authService.validateUser(body.email, body.password);
    if (!user) {
      throw new UnauthorizedException("Tài khoản không đúng");
    }
    return this.authService.login(user);
  }
}
```

---

## ✅ 7. Bảo vệ route bằng JWT

### 📁 jwt.strategy.ts

```ts
@Injectable()
// `extends PassportStrategy(Strategy, [_tên_])` sẽ tự động được sử dụng khi router gọi `@UseGuards(AuthGuard([_tên_]))`
export class JwtStrategy extends PassportStrategy(Strategy, "jwt") {
  constructor() {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: "secret_key",
    });
  }

  async validate(payload: any) {
    return { userId: payload.sub, email: payload.email };
  }
}
```

---

## ✅ 8. Sử dụng bảo vệ route trong controller

```ts
@UseGuards(AuthGuard('jwt'))  // Dựa vào tên để sử dụng
@Get('me')
getProfile(@Request() req) {
  return req.user;
}
```

---

## 🧠 Tổng kết flow hoạt động

1. Client gửi email/password → `POST /auth/login`
2. Server check → nếu đúng thì trả về `access_token`
3. Client lưu token → dùng `Authorization: Bearer <token>` để gọi API
4. Các route được `@UseGuards(AuthGuard('jwt'))` sẽ tự kiểm tra token

## Logger
