# Validation & Pipes

## 1. Built-in Pipes

### ParseIntPipe
```ts
@Get(':id')
getUser(@Param('id', ParseIntPipe) id: number) {
  return this.userService.findById(id);
}
```

### ParseBoolPipe
```ts
@Get()
getUsers(@Query('active', ParseBoolPipe) active: boolean) {
  return this.userService.findByStatus(active);
}
```

### ValidationPipe
```ts
// main.ts - Global validation
app.useGlobalPipes(new ValidationPipe({
  whitelist: true,        // Loại bỏ properties không có decorator
  forbidNonWhitelisted: true, // Throw error nếu có extra properties
  transform: true,        // Tự động transform types
}));
```

---

## 2. DTO Validation với class-validator

### Basic Validation
```ts
import { IsString, IsEmail, IsInt, Min, <PERSON>, IsOptional } from 'class-validator';

export class CreateUserDto {
  @IsString()
  @Length(2, 50)
  name: string;

  @IsEmail()
  email: string;

  @IsInt()
  @Min(18)
  @Max(100)
  age: number;

  @IsOptional()
  @IsString()
  bio?: string;
}
```

### Advanced Validation
```ts
import { IsArray, ValidateNested, IsEnum, Matches } from 'class-validator';
import { Type } from 'class-transformer';

enum UserRole {
  ADMIN = 'admin',
  USER = 'user'
}

export class CreateUserDto {
  @IsString()
  @Matches(/^[a-zA-Z0-9_]+$/, { message: 'Username chỉ chứa chữ, số và _' })
  username: string;

  @IsEnum(UserRole)
  role: UserRole;

  @IsArray()
  @IsString({ each: true })
  tags: string[];

  @ValidateNested()
  @Type(() => AddressDto)
  address: AddressDto;
}

class AddressDto {
  @IsString()
  street: string;

  @IsString()
  city: string;
}
```

---

## 3. Custom Validation Decorators

### Simple Custom Validator
```ts
import { registerDecorator, ValidationOptions, ValidationArguments } from 'class-validator';

export function IsStrongPassword(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'isStrongPassword',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          const strongPasswordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
          return typeof value === 'string' && strongPasswordRegex.test(value);
        },
        defaultMessage(args: ValidationArguments) {
          return 'Password phải có ít nhất 8 ký tự, bao gồm chữ hoa, chữ thường, số và ký tự đặc biệt';
        },
      },
    });
  };
}

// Sử dụng
export class CreateUserDto {
  @IsStrongPassword()
  password: string;
}
```

### Async Custom Validator
```ts
import { Injectable } from '@nestjs/common';
import { ValidatorConstraint, ValidatorConstraintInterface, ValidationArguments } from 'class-validator';

@ValidatorConstraint({ name: 'isEmailUnique', async: true })
@Injectable()
export class IsEmailUniqueConstraint implements ValidatorConstraintInterface {
  constructor(private userService: UserService) {}

  async validate(email: string, args: ValidationArguments) {
    const user = await this.userService.findByEmail(email);
    return !user; // true nếu email chưa tồn tại
  }

  defaultMessage(args: ValidationArguments) {
    return 'Email đã tồn tại';
  }
}

// Decorator
export function IsEmailUnique(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [],
      validator: IsEmailUniqueConstraint,
    });
  };
}
```

---

## 4. Custom Pipes

### Transform Pipe
```ts
import { PipeTransform, Injectable, ArgumentMetadata, BadRequestException } from '@nestjs/common';

@Injectable()
export class ParseDatePipe implements PipeTransform<string, Date> {
  transform(value: string, metadata: ArgumentMetadata): Date {
    const date = new Date(value);
    
    if (isNaN(date.getTime())) {
      throw new BadRequestException('Invalid date format');
    }
    
    return date;
  }
}

// Sử dụng
@Get()
getEvents(@Query('date', ParseDatePipe) date: Date) {
  return this.eventService.findByDate(date);
}
```

### Validation Pipe với Schema
```ts
import { PipeTransform, Injectable, ArgumentMetadata, BadRequestException } from '@nestjs/common';
import { ObjectSchema } from 'joi';

@Injectable()
export class JoiValidationPipe implements PipeTransform {
  constructor(private schema: ObjectSchema) {}

  transform(value: any, metadata: ArgumentMetadata) {
    const { error } = this.schema.validate(value);
    
    if (error) {
      throw new BadRequestException('Validation failed');
    }
    
    return value;
  }
}

// Sử dụng với Joi schema
import * as Joi from 'joi';

const createUserSchema = Joi.object({
  name: Joi.string().required(),
  email: Joi.string().email().required(),
  age: Joi.number().min(18).max(100).required(),
});

@Post()
create(@Body(new JoiValidationPipe(createUserSchema)) createUserDto: CreateUserDto) {
  return this.userService.create(createUserDto);
}
```

---

## 5. Error Handling cho Validation

### Custom Exception Filter
```ts
import { ExceptionFilter, Catch, ArgumentsHost, BadRequestException } from '@nestjs/common';
import { Response } from 'express';

@Catch(BadRequestException)
export class ValidationExceptionFilter implements ExceptionFilter {
  catch(exception: BadRequestException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const status = exception.getStatus();
    const exceptionResponse = exception.getResponse();

    // Format validation errors
    if (typeof exceptionResponse === 'object' && exceptionResponse['message']) {
      const validationErrors = exceptionResponse['message'];
      
      response.status(status).json({
        statusCode: status,
        error: 'Validation Error',
        message: 'Dữ liệu không hợp lệ',
        details: validationErrors,
        timestamp: new Date().toISOString(),
      });
    } else {
      response.status(status).json(exceptionResponse);
    }
  }
}
```

---

## 6. Best Practices

### 1. DTO Inheritance
```ts
export class CreateUserDto {
  @IsString()
  name: string;

  @IsEmail()
  email: string;
}

export class UpdateUserDto extends PartialType(CreateUserDto) {
  @IsOptional()
  @IsString()
  bio?: string;
}
```

### 2. Validation Groups
```ts
export class CreateUserDto {
  @IsString({ groups: ['create'] })
  name: string;

  @IsEmail({ groups: ['create', 'update'] })
  email: string;

  @IsOptional({ groups: ['update'] })
  @IsString({ groups: ['update'] })
  bio?: string;
}

// Sử dụng
@Post()
create(@Body(new ValidationPipe({ groups: ['create'] })) dto: CreateUserDto) {
  return this.userService.create(dto);
}
```

### 3. Transform Options
```ts
// main.ts
app.useGlobalPipes(new ValidationPipe({
  transform: true,
  transformOptions: {
    enableImplicitConversion: true, // Tự động convert types
  },
  whitelist: true,
  forbidNonWhitelisted: true,
}));
```

---

## 7. Tổng kết

| Pipe Type | Mục đích | Ví dụ |
|-----------|----------|-------|
| ParseIntPipe | Convert string → number | `@Param('id', ParseIntPipe)` |
| ValidationPipe | Validate DTO | `@Body() dto: CreateUserDto` |
| Custom Pipe | Logic tùy chỉnh | Transform, validate đặc biệt |

**Best Practices:**
- ✅ Sử dụng ValidationPipe globally
- ✅ Tạo custom validators cho business logic
- ✅ Sử dụng DTO inheritance để tái sử dụng
- ✅ Handle validation errors gracefully
- ✅ Transform data types automatically
