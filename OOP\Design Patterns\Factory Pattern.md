# Factory Pattern

- Tạo ra `đối tượng` mà không cần chỉ định rõ `loại đối tượng cụ thể` nào sẽ được tạo ra.
- `Factory Pattern` sử dụng một `phương thức` hoặc `hàm` để tạo ra các `đối tượng` dựa trên các `điều kiện` được đưa ra hoặc dựa trên dữ liệu đầu vào.
- Tạo đối tượng mà không cần biết chi tiết về cách đối tượng được tạo

```js
// Định nghĩa một factory function
function createShape(type) {
  if (type === "circle") {
    return new Circle();
  } else if (type === "square") {
    return new Square();
  } else if (type === "triangle") {
    return new Triangle();
  } else {
    throw new Error("Invalid shape type");
  }
}

// Không cần chỉ định đối tượng (new Circle), chỉ cần truyền type 'circle'
const shape1 = createShape("circle");
```

## Ứng dụng

- Khi chỉ muốn truyền `type` để tạo ra `đối tượng` từ 1 hàm mà không cần `gọi trực tiếp hàm tạo đối tượng`
- Không quan tâm đối tượng được tạo ra như thế nào
