# Error Boundaries

Error Boundaries là React components bắt JavaScript errors ở bất kỳ đâu trong component tree, log errors, và hiển thị fallback UI.

## 🎯 **Error Boundary cơ bản**

### Class Component Error Boundary

```jsx
import React from 'react';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error) {
    // Cập nhật state để hiển thị fallback UI
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // Log error details
    console.error('Error Boundary caught an error:', error, errorInfo);
    
    this.setState({
      error: error,
      errorInfo: errorInfo
    });
    
    // Có thể gửi error lên error reporting service
    // logErrorToService(error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="error-boundary">
          <h2>Something went wrong!</h2>
          <details style={{ whiteSpace: 'pre-wrap' }}>
            {this.state.error && this.state.error.toString()}
            <br />
            {this.state.errorInfo.componentStack}
          </details>
        </div>
      );
    }

    return this.props.children;
  }
}

// Sử dụng
function App() {
  return (
    <ErrorBoundary>
      <Header />
      <MainContent />
      <Footer />
    </ErrorBoundary>
  );
}
```

### Với react-error-boundary Library

```jsx
import { ErrorBoundary } from 'react-error-boundary';

function ErrorFallback({ error, resetErrorBoundary }) {
  return (
    <div role="alert" className="error-fallback">
      <h2>Oops! Something went wrong</h2>
      <pre>{error.message}</pre>
      <button onClick={resetErrorBoundary}>Try again</button>
    </div>
  );
}

function App() {
  return (
    <ErrorBoundary
      FallbackComponent={ErrorFallback}
      onError={(error, errorInfo) => {
        console.error('Error caught by boundary:', error, errorInfo);
      }}
      onReset={() => {
        // Reset app state nếu cần
        window.location.reload();
      }}
    >
      <MyApp />
    </ErrorBoundary>
  );
}
```

## 🎨 **Advanced Error Boundaries**

### Multiple Error Boundaries

```jsx
function App() {
  return (
    <div>
      {/* Global error boundary */}
      <ErrorBoundary
        fallback={<GlobalErrorFallback />}
        onError={logGlobalError}
      >
        <Header />
        
        {/* Feature-specific error boundary */}
        <ErrorBoundary
          fallback={<SidebarErrorFallback />}
          onError={logSidebarError}
        >
          <Sidebar />
        </ErrorBoundary>
        
        <main>
          <ErrorBoundary
            fallback={<ContentErrorFallback />}
            onError={logContentError}
          >
            <MainContent />
          </ErrorBoundary>
        </main>
        
        <Footer />
      </ErrorBoundary>
    </div>
  );
}
```

### Error Boundary với Retry Logic

```jsx
import { useState } from 'react';
import { ErrorBoundary } from 'react-error-boundary';

function RetryErrorBoundary({ children, maxRetries = 3 }) {
  const [retryCount, setRetryCount] = useState(0);

  const handleError = (error, errorInfo) => {
    console.error(`Error (attempt ${retryCount + 1}):`, error);
    
    if (retryCount < maxRetries) {
      // Auto retry sau 2 giây
      setTimeout(() => {
        setRetryCount(prev => prev + 1);
      }, 2000);
    }
  };

  const handleReset = () => {
    setRetryCount(0);
  };

  const ErrorFallback = ({ error, resetErrorBoundary }) => {
    const canRetry = retryCount < maxRetries;
    
    return (
      <div className="error-fallback">
        <h2>Something went wrong</h2>
        <p>{error.message}</p>
        
        {canRetry ? (
          <div>
            <p>Retrying... (Attempt {retryCount + 1}/{maxRetries + 1})</p>
            <button onClick={resetErrorBoundary}>Retry now</button>
          </div>
        ) : (
          <div>
            <p>Max retries reached. Please refresh the page.</p>
            <button onClick={() => window.location.reload()}>
              Refresh Page
            </button>
          </div>
        )}
      </div>
    );
  };

  return (
    <ErrorBoundary
      FallbackComponent={ErrorFallback}
      onError={handleError}
      onReset={handleReset}
      resetKeys={[retryCount]} // Reset khi retryCount thay đổi
    >
      {children}
    </ErrorBoundary>
  );
}
```

### Async Error Boundary

```jsx
import { useState, useEffect } from 'react';

function useAsyncError() {
  const [, setError] = useState();
  
  return (error) => {
    setError(() => {
      throw error; // Trigger error boundary
    });
  };
}

function AsyncComponent() {
  const throwAsyncError = useAsyncError();
  
  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await fetch('/api/data');
        if (!response.ok) {
          throw new Error('Failed to fetch data');
        }
        const data = await response.json();
        // Handle data...
      } catch (error) {
        throwAsyncError(error); // Throw error to boundary
      }
    };
    
    fetchData();
  }, [throwAsyncError]);
  
  return <div>Async Component</div>;
}

// Sử dụng
function App() {
  return (
    <ErrorBoundary fallback={<div>Error loading data</div>}>
      <AsyncComponent />
    </ErrorBoundary>
  );
}
```

## 🔧 **Error Boundary Patterns**

### Feature-based Error Boundaries

```jsx
// components/FeatureErrorBoundary.jsx
function FeatureErrorBoundary({ 
  children, 
  featureName, 
  fallback: Fallback 
}) {
  const handleError = (error, errorInfo) => {
    // Log với feature context
    console.error(`Error in ${featureName}:`, error);
    
    // Send to monitoring service
    errorReportingService.captureException(error, {
      tags: { feature: featureName },
      extra: errorInfo
    });
  };

  return (
    <ErrorBoundary
      FallbackComponent={Fallback || DefaultFeatureFallback}
      onError={handleError}
    >
      {children}
    </ErrorBoundary>
  );
}

// Sử dụng
function Dashboard() {
  return (
    <div>
      <FeatureErrorBoundary 
        featureName="user-profile"
        fallback={UserProfileError}
      >
        <UserProfile />
      </FeatureErrorBoundary>
      
      <FeatureErrorBoundary 
        featureName="notifications"
        fallback={NotificationsError}
      >
        <NotificationsList />
      </FeatureErrorBoundary>
    </div>
  );
}
```

### Error Boundary với Context

```jsx
import { createContext, useContext, useState } from 'react';

const ErrorContext = createContext();

export function ErrorProvider({ children }) {
  const [errors, setErrors] = useState([]);
  
  const addError = (error) => {
    setErrors(prev => [...prev, { id: Date.now(), error }]);
  };
  
  const removeError = (id) => {
    setErrors(prev => prev.filter(err => err.id !== id));
  };
  
  const clearErrors = () => {
    setErrors([]);
  };
  
  return (
    <ErrorContext.Provider value={{ 
      errors, 
      addError, 
      removeError, 
      clearErrors 
    }}>
      {children}
    </ErrorContext.Provider>
  );
}

export function useError() {
  const context = useContext(ErrorContext);
  if (!context) {
    throw new Error('useError must be used within ErrorProvider');
  }
  return context;
}

// Error Boundary sử dụng context
function ContextErrorBoundary({ children }) {
  const { addError } = useError();
  
  const handleError = (error, errorInfo) => {
    addError({ error, errorInfo, timestamp: new Date() });
  };
  
  return (
    <ErrorBoundary
      fallback={<div>Something went wrong</div>}
      onError={handleError}
    >
      {children}
    </ErrorBoundary>
  );
}
```

## 🎯 **Error Types & Handling**

### Network Errors

```jsx
function NetworkErrorBoundary({ children }) {
  const NetworkErrorFallback = ({ error, resetErrorBoundary }) => {
    const isNetworkError = error.message.includes('fetch') || 
                          error.message.includes('network');
    
    if (isNetworkError) {
      return (
        <div className="network-error">
          <h3>Connection Problem</h3>
          <p>Please check your internet connection and try again.</p>
          <button onClick={resetErrorBoundary}>Retry</button>
        </div>
      );
    }
    
    // Fallback cho errors khác
    return (
      <div className="generic-error">
        <h3>Something went wrong</h3>
        <p>{error.message}</p>
        <button onClick={resetErrorBoundary}>Try again</button>
      </div>
    );
  };
  
  return (
    <ErrorBoundary FallbackComponent={NetworkErrorFallback}>
      {children}
    </ErrorBoundary>
  );
}
```

### Chunk Load Errors (Code Splitting)

```jsx
function ChunkErrorBoundary({ children }) {
  const ChunkErrorFallback = ({ error, resetErrorBoundary }) => {
    const isChunkError = error.message.includes('Loading chunk') ||
                        error.message.includes('ChunkLoadError');
    
    if (isChunkError) {
      return (
        <div className="chunk-error">
          <h3>Update Available</h3>
          <p>A new version is available. Please refresh to continue.</p>
          <button onClick={() => window.location.reload()}>
            Refresh Page
          </button>
        </div>
      );
    }
    
    return (
      <div className="generic-error">
        <h3>Error</h3>
        <p>{error.message}</p>
        <button onClick={resetErrorBoundary}>Try again</button>
      </div>
    );
  };
  
  return (
    <ErrorBoundary FallbackComponent={ChunkErrorFallback}>
      {children}
    </ErrorBoundary>
  );
}
```

## 📋 **Best Practices**

### 1. Error Boundary Placement

```jsx
// ✅ Tốt: Multiple granular boundaries
function App() {
  return (
    <ErrorBoundary fallback={<AppError />}>
      <Header />
      
      <ErrorBoundary fallback={<SidebarError />}>
        <Sidebar />
      </ErrorBoundary>
      
      <ErrorBoundary fallback={<MainError />}>
        <MainContent />
      </ErrorBoundary>
    </ErrorBoundary>
  );
}

// ❌ Không tốt: Chỉ một boundary cho toàn app
function App() {
  return (
    <ErrorBoundary fallback={<div>Error</div>}>
      <Header />
      <Sidebar />
      <MainContent />
    </ErrorBoundary>
  );
}
```

### 2. Error Logging

```jsx
function logError(error, errorInfo) {
  // Development
  if (process.env.NODE_ENV === 'development') {
    console.error('Error:', error);
    console.error('Error Info:', errorInfo);
  }
  
  // Production
  if (process.env.NODE_ENV === 'production') {
    // Send to error monitoring service
    errorService.captureException(error, {
      extra: errorInfo,
      tags: {
        component: errorInfo.componentStack.split('\n')[1]
      }
    });
  }
}
```

### 3. User-friendly Error Messages

```jsx
function UserFriendlyErrorFallback({ error }) {
  const getErrorMessage = (error) => {
    if (error.message.includes('Network')) {
      return 'Please check your internet connection and try again.';
    }
    
    if (error.message.includes('Permission')) {
      return 'You don\'t have permission to access this resource.';
    }
    
    return 'Something unexpected happened. Our team has been notified.';
  };
  
  return (
    <div className="error-fallback">
      <h2>Oops!</h2>
      <p>{getErrorMessage(error)}</p>
      <button onClick={() => window.location.reload()}>
        Refresh Page
      </button>
    </div>
  );
}
```

## 🚨 **Limitations**

Error Boundaries **KHÔNG** bắt errors trong:

1. **Event handlers**
2. **Async code** (setTimeout, promises)
3. **Server-side rendering**
4. **Errors trong chính Error Boundary**

```jsx
// ❌ Không được bắt bởi Error Boundary
function ProblematicComponent() {
  const handleClick = () => {
    throw new Error('Event handler error'); // Không bắt được
  };
  
  useEffect(() => {
    setTimeout(() => {
      throw new Error('Async error'); // Không bắt được
    }, 1000);
  }, []);
  
  return <button onClick={handleClick}>Click me</button>;
}

// ✅ Cách xử lý đúng
function SafeComponent() {
  const throwAsyncError = useAsyncError();
  
  const handleClick = () => {
    try {
      // Risky operation
    } catch (error) {
      throwAsyncError(error); // Throw lên Error Boundary
    }
  };
  
  useEffect(() => {
    const timer = setTimeout(() => {
      try {
        // Risky async operation
      } catch (error) {
        throwAsyncError(error);
      }
    }, 1000);
    
    return () => clearTimeout(timer);
  }, [throwAsyncError]);
  
  return <button onClick={handleClick}>Click me</button>;
}
```
