# SEO title và description

- Sử dụng: `SEO META in 1 Click`

- `title`: L<PERSON> phần `text` hiển chữ lớn khi search GG (Click vào để truy cập), hoặc `text` trên `thanh tab` của trang
- `description`: Là phần text hiển thị mô tả trang bên dưới `title`

```text
Các phương pháp này chỉ dùng được cho `Sever Component`
```

## Sử dụng Metadata của NextJS

```tsx
// Đặt trong các page muốn SEO
import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "Web bán hàng",
  description: "Được tạo bởi Quân - NA",
};
```

## Cấu trúc title động

```tsx
// Đặt cấu trúc này tại Root layout
export const metadata: Metadata = {
  title: {
    template: "%s | Productic", // Nếu trang con có khai báo `title` gắn title vào `%s`
    default: "Productic", // Nếu trang con không khai báo `title` thì lấy mặc định này
  },
  description: "Được tạo bởi Quân - NA",
};
```

## Tạo title động bằng Gennerate Metadata

- Fetch API để lấy data truyền vào Metadata

```tsx
import { Metadata, ResolvingMetadata } from "next";

type Props = {
  params: { id: string };
  searchParams: { [key: string]: string | string[] | undefined };
};

// params là param của URL
export async function generateMetadata(
  { params, searchParams }: Props,
  parent: ResolvingMetadata
): Promise<Metadata> {
  // Gọi API Detail
  const { payload } = await productApiRequest.getDetail(Number(params.id));
  const product = payload.data;

  // Trả về Metadata
  return {
    title: product.name,
  };
}
```

## Cấp độ ưu tiên

1. `app/layout.tsx` (Root layout)
2. `app/blog/layout.tsx` (Nested Blog layout)
3. `app/blog/[slug]/page.tsx` (Blog layout)
