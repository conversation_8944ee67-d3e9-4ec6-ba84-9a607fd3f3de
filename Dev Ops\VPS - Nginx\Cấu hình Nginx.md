# Nginx

`https://duthanhduoc.com/blog/deploy-website-nextjs-hoac-nodejs-len-vps`

- Nginx là 1 `* web server` nằm trong web Hosting

  _`* web server`: là phần mềm hoặc phần cứng sử dụng để chạy các ứng dụng web, nhận các yêu cầu HTTP từ Client và chuyển tiếp đến ứng dụng web được triển khai trên Docker hoặc VPS_

## Cài đặt Nginx

```bash
sudo apt-get update && sudo apt-get install nginx
```

## Cấu hình tường lửa (firewall)

- Cài đặt `Nginx Full`

```bash
sudo ufw allow 'Nginx Full'
```

- Kiểm tra thay đổi

```bash
sudo ufw status
# Kết quả Nginx Full được list ra ở output
```

- Nếu trả về `inactive` nghĩa là tường lửa bị tắ<PERSON>, cầ<PERSON> b<PERSON>t lên như sau:

```bash
# Mở port 22 (ssh), Nginx Full mở rồi không cần mở lại
sudo ufw allow ssh
# Bật tường lửa, nhưng cái này chỉ bật trong phiên làm việc hiện tại thôi, reboot là nó tự tắt
sudo ufw enable
# Kiểm tra trạng thái tường lửa
sudo ufw status
# Yêu cầu tường lửa lên mỗi khi khởi động lại server
sudo systemctl enable ufw
```

## Test Nginx Web Server

```bash
systemctl status nginx
```

- Trả về `active (running)` là thành công

```bash
● nginx.service - A high performance web server and a reverse proxy server
    Loaded: loaded (/lib/systemd/system/nginx.service; enabled; vendor preset: enabled)
    Active: active (running) since Mon 2016-04-18 16:14:00 EDT; 4min 2s ago
  Main PID: 12857 (nginx)
    CGroup: /system.slice/nginx.service
      ├─12857 nginx: master process /usr/sbin/nginx -g daemon on; master_process on
      └─12858 nginx: worker process
```

## Cấu hình Nginx làm Reverse Proxy

- Cho phép người dùng truy cập bằng domain

- Đầu tiên cần di chuyển vào thư mục `/etc/nginx/sites-available`
- Tạo file cấu hình

```bash
# `example.com` là tên file, có thể thay đổi thành tên domain
sudo touch example.com
```

- Mở file và thêm nội dung

```bash
# Mở file vừa tạo
sudo nano example.com

# Thêm nội dung, sửa `example.com` thành tên domain
server {
        listen 80;
        listen [::]:80;

        root /var/www/html;
        index index.html index.htm index.nginx-debian.html;

        server_name example.com www.example.com;

        location / {
                proxy_pass http://localhost:3000;
                proxy_http_version 1.1;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection 'upgrade';
                proxy_set_header Host $host;
                proxy_cache_bypass $http_upgrade;
        }
}
```

- Lưu và thoát bằng cách nhấn tổ hợp Ctrl X -> Y -> Enter

- Tạo 1 file tương tự cho `/etc/nginx/sites-enabled/` bằng cách:

```bash
# `example.com` là tên file, có thể thay đổi thành tên domain
sudo ln -s /etc/nginx/sites-available/example.com /etc/nginx/sites-enabled/
```

- Tránh `hash bucket memory` bằng cách

```bash
# Mở file
sudo nano /etc/nginx/nginx.conf
# Tìm dòng `server_names_hash_bucket_size 64;` và mở nó lên
```

- Kiểm tra lỗi cú pháp

```bash
sudo nginx -t
```

- Sau đó restart Nginx để enable thay đổi

```bash
sudo systemctl restart nginx
```
