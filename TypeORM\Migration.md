# Migration

(<PERSON><PERSON> cập nhật lại CSDL khi có thay đổi về cấu trúc)

## Sử dụng CLI của TypeORM

- Tự động mọi lúc, phù hợp dự án thay đổi liên tục

1. Tạo migration từ entity

```bash
npx typeorm migration:generate -d src/data-source.ts src/migrations/CreateUserTable
```

2. Chạy migration

```bash
npx typeorm migration:run -d src/data-source.ts
```

## Sử dụng SQL thuần

- Chạy mỗi lần build lại dự án
