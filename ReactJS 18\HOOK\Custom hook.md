# Tạo custom hook useStae với useRef

- Lưu giá trị của state vào ref

```js
import { useState, useRef, useCallback } from "react";

function useStateRef(initialValue) {
  const [state, setState] = useState(initialValue);
  const ref = useRef(state);

  const setRefState = useCallback((value) => {
    ref.current = typeof value === "function" ? value(ref.current) : value;
    setState(ref.current);
  }, []);

  return [state, setRefState, ref];
}

export default useStateRef;
```

## Cách dùng

- <PERSON><PERSON> lấy giá trị mới nhất khi bất đồng bộ

```js
function Counter() {
  const [count, setCount, countRef] = useStateRef(0);

  const handleClick = () => {
    setCount(count + 1);
    console.log("countRef:", countRef.current); // L<PERSON>y đư<PERSON> giá trị mới luôn
    console.log("Count:", count); // Vẫn là giá trị cũ cho đến khi re-render vì State batching
  };

  return (
    <div>
      <p>Count: {count}</p>
      <button onClick={handleClick}>Increase Count</button>
    </div>
  );
}
```

## So sánh

### Hàm thông thường

- Không sử dụng Hook
- Không render JSX

### Component

- Phải sử dụng Hook hoặc render JSX

### Custom Hook

- Sử dụng từ khóa `use`
- Nên sử dụng Hook
- Không render JSX
