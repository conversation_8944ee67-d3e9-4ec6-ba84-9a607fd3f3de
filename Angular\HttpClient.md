# HttpClient trong Angular

## 1. G<PERSON><PERSON><PERSON> thiệu

- `HttpClient` là service được Angular cung cấp sẵn trong `@angular/common/http` để gửi và nhận HTTP request (GET, POST, PUT, DELETE,...).
- <PERSON><PERSON><PERSON> việc dưới dạng `Observable` nên có thể dùng `.subscribe()` hoặc `async pipe`.

---

## 2. C<PERSON>u hình HttpClientModule

```ts
// app.module.ts
import { HttpClientModule } from '@angular/common/http';

@NgModule({
  imports: [HttpClientModule]
})
export class AppModule {}
```

---

## 3. <PERSON><PERSON> dụng cơ bản

### Inject HttpClient vào constructor

```ts
import { HttpClient } from '@angular/common/http';

constructor(private http: HttpClient) {}
```

### Các phương thức phổ biến

```ts
this.http.get('api/users').subscribe();
this.http.post('api/users', data).subscribe();
this.http.put('api/users/1', data).subscribe();
this.http.delete('api/users/1').subscribe();
```

---

## 4. Ví dụ hoàn chỉnh

```ts
@Injectable({
  providedIn: 'root'
})
export class UserService {
  private API = 'https://jsonplaceholder.typicode.com/users';

  constructor(private http: HttpClient) {}

  getUsers() {
    return this.http.get(this.API);
  }

  createUser(user: any) {
    return this.http.post(this.API, user);
  }

  updateUser(id: number, user: any) {
    return this.http.put(`${this.API}/${id}`, user);
  }

  deleteUser(id: number) {
    return this.http.delete(`${this.API}/${id}`);
  }
}
```

---

## 5. Sử dụng với `subscribe()`

```ts
this.userService.getUsers().subscribe(
  (data) => console.log(data),
  (error) => console.error(error)
);
```

---

## 6. Bắt lỗi với `catchError`

```ts
import { catchError } from 'rxjs/operators';
import { throwError } from 'rxjs';

this.http.get('api/users').pipe(
  catchError(error => {
    console.error('Lỗi:', error);
    return throwError(() => new Error('Có lỗi xảy ra'));
  })
).subscribe();
```

---

## 7. Gửi header, params

```ts
const headers = new HttpHeaders().set('Authorization', 'Bearer token');
const params = new HttpParams().set('page', '1');

this.http.get('api/users', { headers, params }).subscribe();
```

---

## 8. So sánh `fetch()` vs `HttpClient`

| Tiêu chí           | fetch()                 | Angular HttpClient        |
|--------------------|-------------------------|----------------------------|
| Dựa trên           | Promise                 | Observable (RxJS)         |
| Tích hợp DI        | ❌ Không                | ✅ Có                      |
| Tích hợp Interceptor| ❌ Không               | ✅ Có                      |
| Dễ test, mở rộng   | ❌                      | ✅                         |

---

## 9. Tổng kết

- Luôn import `HttpClientModule` một lần trong `AppModule`.
- `HttpClient` sử dụng RxJS để xử lý bất đồng bộ.
- Có thể dùng thêm `interceptor` để can thiệp request/response (auth, log,...).