| **Pattern**    | **<PERSON><PERSON><PERSON> đích ch<PERSON>h**                                 |
| -------------- | -------------------------------------------------- |
| **Factory**    | Tạo đối tượng dựa vào điều kiện                    |
| **Facade**     | Đơn giản hóa việc sử dụng hệ thống phức tạp        |
| **Observer**   | Theo dõi và phản ứng khi dữ liệu thay đổi          |
| **Singleton**  | Đảm bảo chỉ có 1 instance duy nhất                 |
| **Decorator**  | Thêm tính năng mà không sửa code gốc               |
| **Module**     | Ẩn dữ liệu, chia nhỏ code, tránh trùng biến        |
