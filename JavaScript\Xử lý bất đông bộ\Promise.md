# Promise() và Promise.all()

## 1. Promise()

- X<PERSON> lý kết quả của 1 hành động `thành công` hay `thất bại`
- `Promise` trả về `resolve` hoặc `reject`
- <PERSON><PERSON> 3 trạng thái:

```text
- Fulfilled: hành động xử lý xong và thành công
- Rejected: hành động xử lý xong và thất bại
- Pending: hành động đang chờ xử lý hoặc bị từ chối
```

- <PERSON><PERSON> tạo 1 promise ta sẽ có cú pháp sau đây :

```js
 var = promise = new Promise(callback);
```

- Trong đó callback là một function có hai tham số truyền vào như sau:

```js
var promise = new Promise((resolve, reject) => {});
```

### Ví dụ

```js
// Khởi tạo Promise
var createPromise = new Promise((resolve, reject) => {
  resolve("Success"); // N<PERSON><PERSON> muốn trả về thành công
  //OR
  reject("Error"); // Nếu muốn trả về thất bại
});

createPromise.then(
    (success) => {
        //Success
    },
    (error) => {
        //Error
    };
);
```

- Hàm `then()` có hai 2 tham số callback là `success` và `error` nhưng bạn cũng có thể sử dụng hàm catch để bắt lỗi
- Nên sử dụng `.catch()` để bắt lỗi thay vì (error) của `.then()`

```js
var promise = new Promise((resolve, reject) => {
  reject("Error!");
});

promise
  .then((message) => {
    console.log(message);
  })
  .catch((message) => {
    console.log(message);
  });
```

## 2. Promise.all()

- Nhận vào 1 mảng `Promise` và trả về kết quả ngay khi 1 `Promise` bị từ chối hoặc tất cả các `Promise` hoàn thành

```js
Promise.all([promise1, promise2])
  .then((data) => console.log(data[0], data[1]))
  .catch((error) => console.log(error));
```

## 3. Promise.allSettled()

- Nhận vào 1 mảng `Promise` và trả về kết quả khi tất cả các `Promise` hoàn thành không cần biết `resolve` hay `reject`

```js
Promise.allSettled([
  Promise.reject("This failed too."),
  Promise.resolve("Ok I did it."),
  Promise.reject("Oopps, error is coming."),
]).then((res) => {
  console.log(`Here's the result: `, res);
});
```

Kết quả: chứa `status`, `value` và `reason`

```js
[
  { status: "rejected", reason: "This failed." },
  { status: "fulfilled", : "Ok I did it." },
  { status: "rejected", reason: "Oopps, error is coming." },
];
```

## 4. Promise.race()

- Nhận vào 1 mảng `Promise`
- Trả về kết quả ngay sau khi có 1 `Promise` `resolve` hoặc `reject`

## 5. Promise.any()

- Nhận vào 1 mảng `Promise`
- Trả về `resolve`: khi 1 `Promise` resolve
- Trả về `reject`: khi tất cả `Promise` reject

## Tổng kêt

![alt](https://topdev-vn.cdn.ampproject.org/ii/AW/s/topdev.vn/blog/wp-content/uploads/2023/01/Promise.jpg)
