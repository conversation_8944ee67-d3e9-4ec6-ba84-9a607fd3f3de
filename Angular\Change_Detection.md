# Change Detection trong Angular

## 1. Change Detection là gì?

- <PERSON><PERSON> chế giúp Angular **theo dõi và cập nhật UI** mỗi khi dữ liệu thay đổi trong component.
- Angular sẽ kiểm tra các binding và cập nhật DOM nếu có thay đổi.

---

## 2. Cách hoạt động

- Angular chạy Change Detection mỗi khi có các **"trigger" (kích hoạt)** như:
  - Sự kiện DOM (click, input,...)
  - G<PERSON>i `setTimeout`, `setInterval`, `Promise`, `Observable`
  - <PERSON><PERSON><PERSON> `NgModel`, `EventEmitter`,...
- Angular sẽ **duyệt lại cây component**, so sánh dữ liệu binding và cập nhật DOM nếu cần.

---

## 3. Change Detection Strategy

### Default (Mặc định)

```ts
@Component({
  changeDetection: ChangeDetectionStrategy.Default
})
```
- Angular sẽ kiểm tra **tất cả component con** mỗi lần có thay đổi.

### OnPush (Tối ưu)

```ts
@Component({
  changeDetection: ChangeDetectionStrategy.OnPush
})
```
- Chỉ check lại component khi có:
  - `@Input` thay đổi (so sánh bằng `===`)
  - Observable trả về giá trị mới (qua `async`)
  - Gọi `markForCheck()` thủ công

---

## 4. So sánh Default vs OnPush

| Tiêu chí                        | Default                         | OnPush                          |
|---------------------------------|----------------------------------|----------------------------------|
| Kiểm tra toàn bộ cây con        | ✅ Có                            | ❌ Không                         |
| Tối ưu hiệu suất                | ❌ Ít                            | ✅ Tốt hơn                       |
| Phù hợp với UI phức tạp         | ❌ Không tốt lắm                 | ✅ Nên dùng                      |
| Cần bất biến (`immutable`)      | ❌ Không cần                     | ✅ Cần (so sánh ===)             |

---

## 5. Dùng OnPush với Observable

```html
<div *ngIf="user$ | async as user">
  {{ user.name }}
</div>
```

```ts
@Component({
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ExampleComponent {
  user$ = this.userService.getUser(); // observable
}
```

---

## 6. Trigger thủ công

### markForCheck()

```ts
constructor(private cd: ChangeDetectorRef) {}

someCallback() {
  this.cd.markForCheck(); // đánh dấu để kiểm tra lại
}
```

### detectChanges()

```ts
this.cd.detectChanges(); // chạy Change Detection ngay lập tức
```

---

## 7. Tổng kết

| Kiến thức                        | Ghi nhớ                                      |
|----------------------------------|-----------------------------------------------|
| Change Detection là gì           | Cơ chế cập nhật UI khi dữ liệu thay đổi       |
| Trigger phổ biến                 | Event, Timer, Observable,...                  |
| `ChangeDetectionStrategy.OnPush` | Tối ưu hiệu suất, cần dữ liệu bất biến        |
| Thủ công gọi CD                  | `markForCheck()`, `detectChanges()`           |

# Signal API

- Khi bạn thay đổi một biến `(không phải @Input)` thì Angular không biết có thay đổi → bạn phải ép render thủ công (`detectChanges()`, `markForCheck()`).
- Angular sử dụng `Change Detection` toàn cục, khiến `component` bị render lại toàn bộ → không tối ưu.
- Thay vào đó sử dụng `signal()`. Nó gần giống `useState()` trong `React`. Sẽ tự động cập nhập lại 1 phần giao diện khi có sự thay đổi
- Khi một `signal` thay đổi, component sẽ không re-render toàn bộ. Chỉ `phần giao diện` hoặc `hàm` sử dụng `signal` bị re-render thôi (React sẽ re-render toàn bộ)

```tsx
// Khởi tạo với signal()
counter = signal(0);

// Lấy giá trị của this.counter
console.log(this.counter())

// Cập nhập với update()
this.counter.update(value => value + 1);

// Sử dụng computed() để tính toán lại khi 1 signal phụ thuộc thay đổi
doubleCounter = computed(() => this.counter() * 2);

// Sử dụng effect() để lắng nghe sự thay đổi của các signal tham chiếu bên trong effect và chạy hàm callback
effect(() => {
  console.log('Counter has changed:', this.counter());
});
```
