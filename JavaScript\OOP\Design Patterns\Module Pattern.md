# Decorator Pattern

Tạo một khối mã độc lập có:
- <PERSON><PERSON> liệu riêng tư (private)
- API công khai (public) để thao tác với dữ liệu đó

🎯 Mục tiêu:

- <PERSON><PERSON><PERSON> gó<PERSON> (Encapsulation) logic và biến bên trong
- Tr<PERSON>h xung đột biến toàn cụ<PERSON> (global)
- Tổ chức code rõ ràng hơn


```tsx
// Sử dụng IIFE để tạo scope, giữ biến private, gọi 1 lần duy nhất
const storageModule = (function () {
  function save(key, value) {
    localStorage.setItem(key, JSON.stringify(value));
  }

  function load(key) {
    return JSON.parse(localStorage.getItem(key));
  }

  return {
    save,
    load,
  };
})();

// Dùng
storageModule.save("user", { name: "<PERSON>u<PERSON>" });
console.log(storageModule.load("user"));
```

## Ứng dụng

- <PERSON><PERSON><PERSON> tại ES6 đã có module riêng, kh<PERSON><PERSON> cần sử dụng nữa