# So sánh App Router và Page Router

| 📌 Tiêu chí                    | **Page Router** (`/pages`)                             | **App Router** (`/app`)                                   |
| ------------------------------ | ------------------------------------------------------ | --------------------------------------------------------- |
| **Giới thiệu**                 | Cách routing cũ, ổn định, dễ dùng                      | Cách routing mới từ Next.js 13+, hiện đại hóa hoàn toàn   |
| **Thư mục chính**              | `/pages`                                               | `/app`                                                    |
| **File tương ứng route**       | `about.js` → `/about`                                  | `about/page.tsx` → `/about`                               |
| **Layout**                     | Không hỗ trợ mặc định, cần tạo thủ công                | Có `layout.tsx`, hỗ trợ nested layout                     |
| **Routing động**               | `[id].js`, `[slug].js`                                 | `[id]/page.tsx`, hỗ trợ nested, group, parallel           |
| **Head & SEO**                 | Dùng `next/head`                                       | Dùng `metadata` export hoặc `generateMetadata()`          |
| **Data fetching - SSG**        | `getStaticProps()`                                     | `fetch()` trong server + `revalidate`                     |
| **Data fetching - SSR**        | `getServerSideProps()`                                 | `fetch()` trong server component                          |
| **Route động build-time**      | `getStaticPaths()`                                     | `generateStaticParams()`                                  |
| **API Routes**                 | Trong `/pages/api` → `hello.js`                        | Trong `/app/api/hello/route.ts`                           |
| **Middleware**                 | `middleware.ts` (có thể dùng)                          | `middleware.ts` (dùng tương tự)                           |
| **Streaming**                  | ❌ Không hỗ trợ                                         | ✅ Có hỗ trợ (bằng React Server Component + `suspense`)    |
| **React Server Component**     | ❌ Không hỗ trợ                                         | ✅ Hỗ trợ mặc định                                         |
| **File đặc biệt**              | Không có                                               | `loading.tsx`, `error.tsx`, `not-found.tsx`, `layout.tsx` |
| **Điều hướng client-side**     | `useRouter()` từ `next/router`                         | `useRouter()` từ `next/navigation`                        |
| **Search Params, Path Params** | `router.query`                                         | `useSearchParams()`, `useParams()`                        |
| **Support lâu dài**            | Vẫn được hỗ trợ nhưng không khuyến khích cho dự án mới | Là tương lai của Next.js – **nên dùng cho dự án mới**     |
