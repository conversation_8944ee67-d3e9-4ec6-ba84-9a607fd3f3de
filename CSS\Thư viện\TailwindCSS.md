# Tailwind CSS

- Chỉ cung cấp các class sẵn có
- Không đi kèm giao diện có sẵn (button, card)
- <PERSON><PERSON> dàng custom với `tailwind.config.js`
- <PERSON><PERSON>n biên dịch lại SCSS sang CSS khi Custom

## Tùy chỉnh

`https://tailwindcss.com/docs/guides/create-react-app`

### 1. Tạo `tailwind.config.js`

```js
module.exports = {
  // Theo dõi các tệp này
  content: ["./src/**/*.{js,jsx,ts,tsx}", "./public/index.html"],
  theme: {
    extend: {
      colors: {
        customBlue: "#1E40AF", // Tạo biến mầu mới
        blue: {
          500: "#1E40AF", // Màu xanh mặc định 500 đã được thay đổi
        },
      },
      spacing: {
        72: "18rem",
        84: "21rem",
        96: "24rem",
      },
    },
  },
  plugins: [],
};
```

### 2. Import CSS của Tailwind vào tệp CSS dự án

- nhập các lớp tiện ích của Tailwind vào file `.css` chính của dự án (thường là `src/index.css`)

```css
@tailwind base;
@tailwind components;
@tailwind utilities;
```

## Sử dụng tùy chỉnh

```js
<button class="bg-customGreen">Custom Button</button>
```
