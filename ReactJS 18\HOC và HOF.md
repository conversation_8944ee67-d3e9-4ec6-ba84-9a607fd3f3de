# HOC

## 1. HOC

- Là 1 hàm nhận vào 1 `Component` và trả về 1 `Component` mới (Như kiểu Extend)
- <PERSON><PERSON><PERSON><PERSON>n thêm props cho 1 component
- <PERSON><PERSON> dụ sử dụng HOC để `gắn tọa độ của con trỏ chuột vào 1 component` bất kì
- Ví dụ: `withRouter`

```jsx
// Component con trỏ chuột
const withMouse = function (Component) {
    return class extends React.Component {
        state = {x: 0, y: 0}

        handleMouseMove = (event) => {
            this.setState({
                x: event.clientX
                y: event.clientY
            })
        }

        render() {
            return (
                <div onMouseMove={this.handleMouseMove}>
                    <Component {...this.props} mouse={this.state}>
                </div>
            )
        }
    }
}

```

```js
// Component muốn sử dụng và cách sử dụng
class MyComponent extends React.Component {
  render() {
    return (
      <div>
        {this.props.mouse.x} : {this.props.mouse.y}
      </div>
    );
  }
}

const CompoentWithMouse = withMouse(MyComponent);
```

## 1. HOF

- Là 1 hàm nhận 1 `Function` hoặc trả về kết quả là 1 `Function`
- Ví dụ `map, filter,...`
- 3 khái niệm quan trọng với HOF: `callback function`, `closure` và `currying`

```js
// Đây là 1 Currying Function
const calculator =
  (inputFunction) =>
  (...args) => {
    // Truyền thêm `args` vào 1 Funtion
    const resultValue = inputFunction(...args);
    return resultValue;
  };
```
