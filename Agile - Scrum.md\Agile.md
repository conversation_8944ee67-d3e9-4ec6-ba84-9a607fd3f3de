# Agile

`https://itviec.com/blog/agile-la-gi-scrum-la-gi/`

- `<PERSON><PERSON><PERSON>ng pháp` phát triển phần mềm linh hoạt

## Tuyên ngôn Agile

- `<PERSON><PERSON> nhân và sự tương tác` hơn là quy trình và công cụ;
- `<PERSON><PERSON><PERSON> mềm chạy tốt` hơn là tài liệu đầy đủ;
- `<PERSON><PERSON><PERSON> tác với khách hàng` hơn là đàm phán hợp đồng;
- `<PERSON>ản hồi với các thay đổi` hơn là bám sát kế hoạch.

## Đặc trưng

### 1. Tính lặp

- Dự án được thực hiện trong các `phân đoạn` lặp đi lặp lại `(Sprint)`
- Trong mỗi phân đoạn, nhóm phát triển thực hiện đầy đủ các công việc nh<PERSON> `l<PERSON><PERSON> kế ho<PERSON>`, `<PERSON>h<PERSON> tích`, `thi<PERSON><PERSON> kế`, `tri<PERSON><PERSON> khai`, `ki<PERSON><PERSON> thử` để cho ra các phần nhỏ của sản phẩm.

### 2. Tính tịnh tiến và tiến hóa

- Cuối các phân đoạn, nhóm phát triển thường cho ra các phần nhỏ của sản phẩm
- Theo thời gian, phân đoạn này tiếp nối phân đoạn kia, các phần chạy được này sẽ được tích lũy, lớn dần lên cho tới khi toàn bộ yêu cầu của khách hàng được thỏa mãn.

### 3. Tính thích nghi

- Do các phân đoạn chỉ kéo dài trong một khoảng thời gian ngắn, và việc lập kế hoạch cũng được điều chỉnh liên tục, nên các thay đổi trong quá trình phát triển đều có thể được đáp ứng theo cách thích hợp.

### 4. Nhóm tự tổ chức và liên chức năng

- Các nhóm này tự thực hiện lấy việc phân công công việc mà không dựa trên các mô tả cứng về chức danh (title) hay làm việc dựa trên một sự phân cấp rõ ràng trong tổ chức.

### 5. Quản lý tiến trình thực nghiệm

- Đưa ra các quyết định dựa trên các dữ liệu thực tiễn thay vì tính toán lý thuyết

### 6. Giao tiếp trực diện

- Ưu tiên trực tiếp nói chuyện với khách hàng để hiểu rõ hơn về cái khách hàng thực sự cần, thay vì phụ thuộc nhiều vào các loại văn bản.

### 7. Phát triển dựa trên giá trị

“phần mềm chạy tốt chính là thước đo của tiến độ”
