# PM2

`https://viblo.asia/p/tong-quan-ve-pm2-trinh-quan-ly-cac-ung-dung-nodejs-djeZ1EYYZWz`
`https://github.com/duocmmo/nodejs-super/blob/main/pm2.md`

- Là trình quản lý tiến trình ứng dụng cho ứng dụng `node` tích hợp `cân bằng tải`
- `Khởi động lại` ứng dụng khi ứng dụng `crash`
- `Tự động khởi động lại` ứng dụng khi `server khởi động lại`
- Monitor các tiến trình NodeJs

## Tính năng chính

- Giám sát ứng dụng
- Quản lý các process, logs của ứng dụng
- Tự động restart/reload app
- <PERSON><PERSON> báo cấu hình qua JSON file
- Tích hợp với Docker
- Cluster mode
- Chạy các kịch bản lệnh (Startup Scripts) cho hệ thống
- <PERSON> phép tích hợp các module cho hệ thống
